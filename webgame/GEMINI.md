# Project: 财富自由之路 (The Road to Financial Freedom)

## Project Overview

This project is a web-based, multiplayer version of the "Cashflow" board game, named "财富自由之路". It is built using the Phaser game framework, TypeScript, and Vite. The game features a client-server architecture, with the frontend client communicating with a WebSocket server (at `ws://localhost:9070/ws`) to handle multiplayer interactions.

The game is rendered into a `div` element with the id `app` in the `index.html` file. The entry point of the application is `src/main.ts`.

**Key Technologies:**

*   **Game Framework:** Phaser 3
*   **Language:** TypeScript
*   **Build Tool:** Vite
*   **Multiplayer:** WebSockets

## Building and Running

To build and run the project, use the following npm scripts defined in `package.json`:

*   **Development:**
    ```bash
    npm run dev
    ```
    This command starts a Vite development server, allowing you to play the game in your browser and benefit from hot-reloading.

*   **Build:**
    ```bash
    npm run build
    ```
    This command compiles the TypeScript code and builds the project for production.

*   **Preview:**
    ```bash
    npm run preview
    ```
    This command starts a local server to preview the production build.

## Development Conventions

*   **Code Style:** The codebase is written in TypeScript and follows standard object-oriented programming principles. The code is organized into scenes (`MainMenuScene`, `RoomScene`, `GameScene`), which is a common pattern in Phaser development.
*   **TypeScript Configuration:** The `tsconfig.json` file is configured for a modern environment (ES2022) and includes strict type checking.
*   **Communication:** The client communicates with the server using a JSON-based WebSocket protocol. The message types are defined and handled in the respective scenes.
*   **State Management:** The game state is managed on the server, and the client receives updates via WebSocket messages. The client then updates the UI to reflect the new game state.
*   **Data Models:** The `src/types/Account.ts` file defines the data models for the game, including `Account`, `Player`, `RoomData`, and `GameState`. These models are used to ensure type safety and consistency between the client and the server.
*   **User Management:** The `src/utils/UserManager.ts` class handles user information. It generates a unique user ID and a random name for new users, and it stores this information in the browser's local storage.

---

- 使用中文沟通