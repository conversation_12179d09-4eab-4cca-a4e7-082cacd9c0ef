/**
 * UserManager 用户管理工具类
 * 
 * 调用场景:
 *   游戏启动时管理用户身份信息，包括生成、存储和读取用户数据
 * 
 * 主要功能:
 *   - 生成唯一用户ID
 *   - 生成随机中文姓名
 *   - 本地存储用户信息
 *   - 读取已存储的用户信息
 */

export interface UserInfo {
  userId: string;
  userName: string;
  createdAt: number;
  lastLoginAt: number;
}

export class UserManager {
  private static readonly STORAGE_KEY = 'cashfree_user_info';
  
  // 随机姓名生成数据
  private static readonly surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '马', '朱', '胡'];
  private static readonly givenNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '涛', '明', '超', '秀', '华'];

  /**
   * 生成唯一用户ID
   * 
   * 调用场景:
   *   用户首次进入游戏时生成唯一标识
   * 
   * 主要逻辑:
   *   使用时间戳和随机数组合生成唯一ID
   */
  private static generateUserId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `user_${timestamp}_${random}`;
  }

  /**
   * 生成随机中文姓名
   * 
   * 调用场景:
   *   用户首次进入游戏时生成随机姓名
   * 
   * 主要逻辑:
   *   从预设的姓氏和名字数组中随机选择组合
   */
  private static generateRandomName(): string {
    const surname = this.surnames[Math.floor(Math.random() * this.surnames.length)];
    const givenName = this.givenNames[Math.floor(Math.random() * this.givenNames.length)];
    return surname + givenName;
  }

  /**
   * 获取或创建用户信息
   * 
   * 调用场景:
   *   游戏启动时调用，获取已存储的用户信息或创建新用户
   * 
   * 主要逻辑:
   *   1. 尝试从本地存储读取用户信息
   *   2. 如果不存在则创建新用户信息
   *   3. 更新最后登录时间
   *   4. 保存到本地存储
   */
  static getUserInfo(): UserInfo {
    try {
      // 尝试从本地存储读取用户信息
      const storedData = localStorage.getItem(this.STORAGE_KEY);
      
      if (storedData) {
        const userInfo: UserInfo = JSON.parse(storedData);
        
        // 验证数据完整性
        if (userInfo.userId && userInfo.userName && userInfo.createdAt) {
          // 更新最后登录时间
          userInfo.lastLoginAt = Date.now();
          this.saveUserInfo(userInfo);
          
          console.log('读取已存储的用户信息:', userInfo);
          return userInfo;
        }
      }
    } catch (error) {
      console.warn('读取用户信息失败，将创建新用户:', error);
    }

    // 创建新用户信息
    const newUserInfo: UserInfo = {
      userId: this.generateUserId(),
      userName: this.generateRandomName(),
      createdAt: Date.now(),
      lastLoginAt: Date.now()
    };

    // 保存到本地存储
    this.saveUserInfo(newUserInfo);
    
    console.log('创建新用户信息:', newUserInfo);
    return newUserInfo;
  }

  /**
   * 保存用户信息到本地存储
   * 
   * 调用场景:
   *   创建新用户或更新用户信息时调用
   * 
   * 主要逻辑:
   *   将用户信息序列化并存储到localStorage
   */
  private static saveUserInfo(userInfo: UserInfo): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(userInfo));
      console.log('用户信息已保存到本地存储');
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }

  /**
   * 更新用户姓名
   * 
   * 调用场景:
   *   用户修改姓名时调用
   * 
   * 主要逻辑:
   *   更新用户姓名并保存到本地存储
   */
  static updateUserName(newName: string): UserInfo {
    const userInfo = this.getUserInfo();
    userInfo.userName = newName;
    userInfo.lastLoginAt = Date.now();
    this.saveUserInfo(userInfo);
    
    console.log('用户姓名已更新:', newName);
    return userInfo;
  }

  /**
   * 清除用户信息
   * 
   * 调用场景:
   *   重置游戏或清除用户数据时调用
   * 
   * 主要逻辑:
   *   从本地存储中删除用户信息
   */
  static clearUserInfo(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('用户信息已清除');
    } catch (error) {
      console.error('清除用户信息失败:', error);
    }
  }

  /**
   * 检查是否为新用户
   * 
   * 调用场景:
   *   判断用户是否首次进入游戏
   * 
   * 主要逻辑:
   *   检查本地存储中是否存在用户信息
   */
  static isNewUser(): boolean {
    try {
      const storedData = localStorage.getItem(this.STORAGE_KEY);
      return !storedData;
    } catch (error) {
      console.warn('检查新用户状态失败:', error);
      return true;
    }
  }
}
