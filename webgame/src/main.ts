import Phaser from 'phaser';
import { MainMenuScene } from './scenes/MainMenuScene';
import { RoomScene } from './scenes/RoomScene';
import { GameScene } from './scenes/GameScene';

// 游戏配置
const config: Phaser.Types.Core.GameConfig = {
  type: Phaser.AUTO, // 自动选择渲染器 (WebGL 或 Canvas)
  width: 1024, // 增加游戏画布宽度以适应新界面
  height: 768, // 增加游戏画布高度以适应新界面
  parent: 'app', // 游戏画布将要附加到的 DOM 元素的 ID
  scene: [MainMenuScene, RoomScene, GameScene], // 场景列表：主菜单 -> 房间 -> 游戏
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { x: 0, y: 0 },
      debug: false
    }
  }
};

// 创建游戏实例
const game = new Phaser.Game(config);

console.log('Phaser game created:', game);
