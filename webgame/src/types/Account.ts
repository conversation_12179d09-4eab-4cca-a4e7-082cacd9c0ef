/**
 * Account 账号模型接口定义
 * 
 * 调用场景:
 *   前端与后端Account模型对应，用于类型安全的账号数据管理
 * 
 * 主要字段:
 *   - id: 账号唯一标识
 *   - username: 登录用户名
 *   - nickname: 显示昵称
 *   - status: 当前状态（离线/在线/在房间/游戏中）
 *   - statistics: 游戏统计数据
 */

export type AccountStatus = 'offline' | 'online' | 'in_room' | 'in_game';

export interface GameStatistics {
  total_games: number;      // 总游戏局数
  win_games: number;        // 胜利局数
  best_cash_flow: number;   // 最佳现金流记录
  average_game_time: number; // 平均游戏时长（秒）
  last_played_at: string;   // 最后游戏时间
}

export interface Account {
  id: string;
  username: string;         // 登录用户名
  nickname: string;         // 显示昵称
  avatar?: string;          // 头像URL
  status: AccountStatus;    // 当前状态
  current_room_id?: string; // 当前房间ID
  statistics: GameStatistics; // 游戏统计数据
  last_login_at: string;    // 最后登录时间
  created_at: string;
  updated_at: string;
}

/**
 * Player 游戏内玩家模型接口定义
 * 
 * 调用场景:
 *   游戏进行时使用，管理单局游戏中的财务状态
 * 
 * 主要字段:
 *   - game_id: 所属游戏局ID
 *   - account_id: 关联的账号ID
 *   - 财务相关字段（现金、资产等）
 */
export interface Asset {
  id: string;
  type: string;           // 资产类型：stock, real_estate, business, precious_metal
  name: string;           // 资产名称
  purchase_price: number; // 购买价格
  current_value: number;  // 当前价值
  monthly_income: number; // 每月被动收入
  quantity: number;       // 数量
}

export interface Income {
  id: string;
  type: string;       // 收入类型：salary, passive, bonus, dividend
  source: string;     // 收入来源
  amount: number;     // 收入金额
  is_passive: boolean; // 是否为被动收入
}

export interface Expense {
  id: string;
  type: string;        // 支出类型：living, loan, tax, insurance
  description: string; // 支出描述
  amount: number;      // 支出金额
  is_fixed: boolean;   // 是否为固定支出
}

export interface Player {
  game_id: string;            // 所属游戏局ID
  account_id: string;         // 关联的账号ID
  profession: string;         // 职业信息
  cash: number;               // 现金余额
  cash_flow: number;          // 每月现金流
  assets: Asset[];            // 资产列表
  incomes: Income[];          // 收入列表
  expenses: Expense[];        // 支出列表
  game_turn: number;          // 当前回合数
  is_financially_free: boolean; // 是否达成财务自由
  created_at: string;         // 游戏开始时间
  updated_at: string;
}

/**
 * 房间数据接口
 */
export interface RoomData {
  room_id: string;
  room_name: string;
  room_owner: string;
  accounts: { [key: string]: Account }; // 房间内的账号列表
  max_players: number;
  is_started: boolean;
}

/**
 * 游戏状态接口
 */
export interface GameState {
  id: string;
  players: { [key: string]: Player }; // 游戏中的玩家列表
  current_month: number;
  phase: string;
  is_started: boolean;
  max_players: number;
  created_at: string;
  updated_at: string;
}
