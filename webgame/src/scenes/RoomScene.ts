import Phaser from 'phaser';
import type { Account, RoomData } from '../types/Account';

/**
 * RoomScene 是房间管理场景
 * 
 * 调用场景:
 *   从MainMenuScene成功加入或创建房间后进入
 * 
 * 主要功能:
 *   - 显示房间信息和玩家列表
 *   - 提供开始游戏按钮（房主功能）
 *   - 实时更新房间状态
 *   - 处理玩家进出房间
 */
export class RoomScene extends Phaser.Scene {
  private socket?: WebSocket;
  private currentAccount: Account | null = null;
  private roomData: RoomData | null = null;
  private gameState: any = null;
  private uiElements: { [key: string]: Phaser.GameObjects.Text } = {};
  private accountsContainer?: Phaser.GameObjects.Container;
  private isRoomOwner: boolean = false;

  /**
   * 构造函数，设置场景的key
   */
  constructor() {
    super('RoomScene');
  }

  /**
   * init 方法，接收从上一个场景传递的数据
   * 
   * 调用场景:
   *   场景启动时自动调用，接收MainMenuScene传递的数据
   * 
   * 主要逻辑:
   *   初始化WebSocket连接、账号信息和房间数据
   */
  init(data: any) {
    this.socket = data.socket;
    this.currentAccount = data.account;
    this.roomData = data.roomData;
    
    // 判断是否为房主（创建房间的玩家）
    this.isRoomOwner = this.roomData?.room_owner === this.currentAccount?.nickname;
    
    console.log('房间场景初始化:', {
      account: this.currentAccount,
      roomData: this.roomData,
      isRoomOwner: this.isRoomOwner,
      hasSocket: !!this.socket
    });
  }

  /**
   * preload 方法，预加载资源
   */
  preload() {
    // 预加载房间场景资源
  }

  /**
   * create 方法，创建房间界面
   * 
   * 调用场景:
   *   场景启动时自动调用
   * 
   * 主要逻辑:
   *   创建房间UI界面并设置WebSocket消息监听
   */
  create() {
    this.createUI();
    this.setupWebSocketHandlers();
    
    // 请求最新的房间状态
    this.requestRoomState();
  }

  /**
   * createUI 创建房间用户界面
   * 
   * 调用场景:
   *   create方法中调用，构建房间管理界面
   * 
   * 主要逻辑:
   *   创建房间信息显示、玩家列表、操作按钮等UI元素
   */
  createUI() {
    const { width, height } = this.scale;

    // 房间标题
    this.add.text(width / 2, 50, '游戏房间', {
      fontSize: '36px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 房间信息区域
    this.uiElements.roomName = this.add.text(width / 2, 100, `房间: ${this.roomData?.room_name || '未知房间'}`, {
      fontSize: '24px',
      color: '#00ff88'
    }).setOrigin(0.5);

    this.uiElements.roomId = this.add.text(width / 2, 130, `房间ID: ${this.roomData?.room_id || ''}`, {
      fontSize: '16px',
      color: '#cccccc'
    }).setOrigin(0.5);

    // 当前玩家信息
    this.add.text(50, 180, '当前玩家:', {
      fontSize: '20px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    this.uiElements.currentPlayer = this.add.text(50, 210, `${this.currentAccount?.nickname || '未知玩家'} ${this.isRoomOwner ? '(房主)' : ''}`, {
      fontSize: '18px',
      color: '#00ff88'
    });

    // 房间状态
    this.uiElements.roomStatus = this.add.text(50, 250, '房间状态: 等待中', {
      fontSize: '18px',
      color: '#ffff00'
    });

    this.uiElements.playerCount = this.add.text(50, 280, '玩家数量: 1/4', {
      fontSize: '18px',
      color: '#ffffff'
    });

    // 玩家列表区域
    this.add.text(width / 2, 330, '房间内玩家', {
      fontSize: '24px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 创建账号列表容器
    this.accountsContainer = this.add.container(width / 2, 380);

    // 操作按钮区域
    const buttonY = height - 150;

    // 开始游戏按钮（仅房主可见）
    if (this.isRoomOwner) {
      const startGameButton = this.add.text(width / 2 - 100, buttonY, '开始游戏', {
        fontSize: '24px',
        color: '#ffffff',
        backgroundColor: '#00cc66',
        padding: { x: 20, y: 10 }
      }).setOrigin(0.5).setInteractive();

      startGameButton.on('pointerdown', () => this.startGame());
      startGameButton.on('pointerover', () => {
        startGameButton.setStyle({ backgroundColor: '#00ff88' });
      });
      startGameButton.on('pointerout', () => {
        startGameButton.setStyle({ backgroundColor: '#00cc66' });
      });
    }

    // 离开房间按钮
    const leaveButton = this.add.text(width / 2 + 100, buttonY, '离开房间', {
      fontSize: '24px',
      color: '#ffffff',
      backgroundColor: '#cc6600',
      padding: { x: 20, y: 10 }
    }).setOrigin(0.5).setInteractive();

    leaveButton.on('pointerdown', () => this.leaveRoom());
    leaveButton.on('pointerover', () => {
      leaveButton.setStyle({ backgroundColor: '#ff8800' });
    });
    leaveButton.on('pointerout', () => {
      leaveButton.setStyle({ backgroundColor: '#cc6600' });
    });

    // 游戏说明
    this.add.text(width / 2, height - 80, '等待其他玩家加入，房主可以开始游戏', {
      fontSize: '16px',
      color: '#888888'
    }).setOrigin(0.5);

    // 返回主菜单提示
    this.add.text(width / 2, height - 50, '离开房间将返回主菜单', {
      fontSize: '14px',
      color: '#666666'
    }).setOrigin(0.5);
  }

  /**
   * setupWebSocketHandlers 设置WebSocket消息处理
   * 
   * 调用场景:
   *   create方法中调用，设置消息监听
   * 
   * 主要逻辑:
   *   重新设置WebSocket的onmessage回调，处理房间相关消息
   */
  setupWebSocketHandlers() {
    if (!this.socket) return;

    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log('房间场景收到消息:', message);
      this.handleServerMessage(message);
    };
  }

  /**
   * handleServerMessage 处理服务器消息
   * 
   * 调用场景:
   *   收到WebSocket消息时调用
   * 
   * 主要逻辑:
   *   处理房间状态更新、游戏开始等消息
   */
  handleServerMessage(message: any) {
    switch (message.type) {
      case 'room.state':
        // 房间状态更新
        this.gameState = message.data;
        this.updateRoomUI();
        break;
      
      case 'game.start':
        // 游戏开始，切换到游戏场景
        console.log('游戏开始，切换到游戏场景');
        this.switchToGameScene();
        break;
      
      case 'room.join':
        // 有新玩家加入
        console.log('新玩家加入房间:', message.data);
        this.requestRoomState(); // 刷新房间状态
        break;
      
      case 'room.leave':
        // 有玩家离开
        console.log('玩家离开房间:', message.data);
        this.requestRoomState(); // 刷新房间状态
        break;
      
      case 'error':
        console.error('服务器错误:', message.data.message);
        // 显示错误信息
        this.showError(message.data.message);
        break;
      
      case 'success':
        console.log('操作成功:', message.data.message);
        break;
      
      default:
        console.log('未处理的消息类型:', message.type);
        break;
    }
  }

  /**
   * requestRoomState 请求房间状态
   * 
   * 调用场景:
   *   场景创建时和玩家进出房间时调用
   * 
   * 主要逻辑:
   *   向服务器请求最新的房间状态信息
   */
  requestRoomState() {
    if (!this.socket) return;

    const message = {
      type: 'room.state',
      player_id: this.currentAccount?.id || '',
      data: {
        room_id: this.roomData?.room_id || ''
      }
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * updateRoomUI 更新房间界面
   * 
   * 调用场景:
   *   收到房间状态更新消息时调用
   * 
   * 主要逻辑:
   *   根据最新的房间状态更新UI显示，适配新的RoomState数据结构
   */
  updateRoomUI() {
    if (!this.gameState) return;

    // 检查场景是否仍然活跃
    if (!this.scene.isActive()) return;

    // 适配新的RoomState数据结构
    const roomState = this.gameState;
    const isGameStarted = roomState.Status === 'in_game' || roomState.status === 'in_game';
    
    // 更新房间状态
    const statusText = isGameStarted ? '游戏中' : '等待中';
    const statusColor = isGameStarted ? '#00ff00' : '#ffff00';
    if (this.uiElements.roomStatus && this.uiElements.roomStatus.active) {
      this.uiElements.roomStatus.setText(`房间状态: ${statusText}`);
      this.uiElements.roomStatus.setColor(statusColor);
    }

    // 更新玩家数量 - 从RoomState.Accounts获取
    const accountCount = Object.keys(roomState.Accounts || roomState.accounts || {}).length;
    const maxPlayers = roomState.MaxPlayers || roomState.max_players || 4;
    if (this.uiElements.playerCount && this.uiElements.playerCount.active) {
      this.uiElements.playerCount.setText(`玩家数量: ${accountCount}/${maxPlayers}`);
    }

    // 更新房间名称
    if (this.uiElements.roomName && this.uiElements.roomName.active) {
      const roomName = roomState.RoomName || roomState.room_name || '未知房间';
      this.uiElements.roomName.setText(`房间: ${roomName}`);
    }

    // 更新玩家列表
    this.updatePlayersList();
  }

  /**
   * updatePlayersList 更新账号列表显示
   * 
   * 调用场景:
   *   updateRoomUI方法中调用
   * 
   * 主要逻辑:
   *   显示房间内所有账号的信息，从RoomState.Accounts中获取数据
   *   适配新的RoomState和Account模型数据结构
   */
  updatePlayersList() {
    if (!this.accountsContainer || !this.gameState) return;
    
    // 检查场景是否仍然活跃
    if (!this.scene.isActive()) return;

    // 检查容器是否仍然有效
    if (!this.accountsContainer.active) return;

    // 清除之前的显示
    this.accountsContainer.removeAll(true);

    // 从RoomState.Accounts中获取账号数据
    const roomState = this.gameState;
    const accounts = Object.values(roomState.Accounts || roomState.accounts || {}) as any[];
    
    console.log('更新玩家列表，账号数据:', accounts);
    
    if (accounts.length === 0) {
      const emptyText = this.add.text(0, 0, '暂无玩家', {
        fontSize: '16px',
        color: '#666666'
      }).setOrigin(0.5);
      this.accountsContainer.add(emptyText);
      return;
    }

    // 显示每个账号的信息
    accounts.forEach((account: any, index: number) => {
      const yOffset = index * 60;
      
      // 获取账号昵称，适配Account模型字段
      let accountName = '未知玩家';
      if (account.Nickname || account.nickname) {
        accountName = account.Nickname || account.nickname;
      } else if (account.UserName || account.user_name) {
        accountName = account.UserName || account.user_name;
      } else if (account.ID || account.id) {
        accountName = account.ID || account.id;
      }
      
      const isCurrentPlayer = (account.ID || account.id) === this.currentAccount?.id || 
                             (account.Nickname || account.nickname) === this.currentAccount?.nickname;
      
      // 判断是否为房主
      const isRoomOwner = (account.ID || account.id) === (roomState.RoomOwner || roomState.room_owner);
      const displayName = isRoomOwner ? `${accountName} (房主)` : accountName;
      
      // 账号昵称
      const nameText = this.add.text(0, yOffset, displayName, {
        fontSize: '20px',
        color: isCurrentPlayer ? '#00ff88' : '#ffffff',
        fontStyle: isCurrentPlayer ? 'bold' : 'normal'
      }).setOrigin(0.5);
      
      // 账号状态信息 - 显示在线状态和统计信息
      let statusInfo = '在线';
      const status = account.Status || account.status;
      if (status) {
        const statusMap: { [key: string]: string } = {
          'online': '在线',
          'in_room': '房间中',
          'in_game': '游戏中',
          'offline': '离线'
        };
        statusInfo = statusMap[status] || status;
      }
      
      // 如果有统计信息，显示游戏统计
      const stats = account.Statistics || account.statistics;
      if (stats && (stats.TotalGames || stats.total_games)) {
        const totalGames = stats.TotalGames || stats.total_games || 0;
        const winGames = stats.WinGames || stats.win_games || 0;
        const winRate = totalGames > 0 ? Math.round((winGames / totalGames) * 100) : 0;
        statusInfo += ` | 总局数: ${totalGames} | 胜率: ${winRate}%`;
      }
      
      const infoText = this.add.text(0, yOffset + 25, statusInfo, {
        fontSize: '14px',
        color: '#cccccc'
      }).setOrigin(0.5);
      
      if (this.accountsContainer && this.accountsContainer.active) {
        this.accountsContainer.add([nameText, infoText]);
      }
    });
  }

  /**
   * startGame 开始游戏
   * 
   * 调用场景:
   *   房主点击开始游戏按钮时调用
   * 
   * 主要逻辑:
   *   向服务器发送开始游戏请求
   */
  startGame() {
    if (!this.socket || !this.isRoomOwner) {
      console.log('只有房主可以开始游戏');
      return;
    }

    const message = {
      type: 'game.start',
      player_id: this.currentAccount?.id || '',
      game_id: this.roomData?.room_id || '',
      data: {}
    };

    this.socket.send(JSON.stringify(message));
    console.log('发送开始游戏请求');
  }

  /**
   * leaveRoom 离开房间
   * 
   * 调用场景:
   *   点击离开房间按钮时调用
   * 
   * 主要逻辑:
   *   向服务器发送离开房间请求，然后返回主菜单
   */
  leaveRoom() {
    if (!this.socket) return;

    // 获取账号ID
    const accountId = this.currentAccount?.id || '';

    const message = {
      type: 'room.leave',
      player_id: accountId,
      data: {
        account_id: accountId,
        room_id: this.roomData?.room_id || ''
      }
    };

    console.log('发送离开房间请求:', message);
    this.socket.send(JSON.stringify(message));
    
    // 返回主菜单
    this.scene.start('MainMenuScene');
  }

  /**
   * switchToGameScene 切换到游戏场景
   * 
   * 调用场景:
   *   收到游戏开始消息时调用
   * 
   * 主要逻辑:
   *   传递WebSocket连接和玩家信息到游戏场景
   */
  switchToGameScene() {
    this.scene.start('GameScene', {
      socket: this.socket,
      account: this.currentAccount, // 传递完整的账号信息
      gameState: this.gameState
    });
  }

  /**
   * showError 显示错误信息
   * 
   * 调用场景:
   *   收到错误消息时调用
   * 
   * 主要逻辑:
   *   在界面上显示错误提示信息
   */
  showError(errorMessage: string) {
    // 创建临时错误提示
    const errorText = this.add.text(this.scale.width / 2, 200, `错误: ${errorMessage}`, {
      fontSize: '18px',
      color: '#ff0000',
      backgroundColor: '#000000',
      padding: { x: 10, y: 5 }
    }).setOrigin(0.5);

    // 3秒后自动消失
    this.time.delayedCall(3000, () => {
      errorText.destroy();
    });
  }
}
