import Phaser from 'phaser';
import { type UserInfo } from '../utils/UserManager';

/**
 * GameScene 是游戏的主要场景。
 *
 * @extends Phaser.Scene
 */
export class GameScene extends Phaser.Scene {
  private socket?: WebSocket;
  private playerData: any = null;
  private gameState: any = null;
  private uiElements: { [key: string]: Phaser.GameObjects.Text } = {};
  private isConnected = false;
  private currentUserInfo: UserInfo | null = null;
  private otherPlayersContainer?: Phaser.GameObjects.Container;

  /**
   * 构造函数，设置场景的 key。
   */
  constructor() {
    super('GameScene');
  }

  /**
   * init 方法，接收从RoomScene传递的数据
   * 
   * 调用场景:
   *   从RoomScene切换到GameScene时调用
   * 
   * 主要逻辑:
   *   初始化WebSocket连接、玩家姓名和游戏状态
   */
  init(data?: any) {
    if (data) {
      this.socket = data.socket;
      this.currentUserInfo = data.userInfo;
      this.gameState = data.gameState || data.gameData;
      this.isConnected = true;
      
      console.log('GameScene初始化，接收数据:', {
        userInfo: this.currentUserInfo,
        hasSocket: !!this.socket,
        hasGameState: !!this.gameState,
        isRejoining: data.isRejoining
      });
    }
  }

  /**
   * preload 方法，在场景创建前预加载资源。
   *
   * 调用场景:
   *   场景启动时由 Phaser 引擎自动调用一次。
   *
   * 主要逻辑:
   *   目前为空，后续可用于加载图片、音频等资源。
   */
  preload() {
    // 例如: this.load.image('logo', 'assets/logo.png');
  }

  /**
   * create 方法，在场景预加载完成后创建游戏对象。
   *
   * 调用场景:
   *   preload 方法执行完毕后由 Phaser 引擎自动调用一次。
   *
   * 主要逻辑:
   *   创建游戏界面。如果没有WebSocket连接则建立连接。
   */
  create() {
    this.createUI();
    
    // 如果没有从RoomScene传递WebSocket连接，则建立新连接
    if (!this.socket || !this.isConnected) {
      this.connectWebSocket();
    } else {
      // 使用现有连接，设置消息处理
      this.setupWebSocketHandlers();
      // 这里应该广播给房间内所有玩家，暂时只回复给发送者
      if (this.socket && this.currentUserInfo) {
        this.uiElements.currentPlayerName.setText(`欢迎，${this.currentUserInfo.userName}！`);
      }
      // 更新连接状态显示
      if (this.uiElements.connectionStatus) {
        this.uiElements.connectionStatus.setText('已连接');
        this.uiElements.connectionStatus.setColor('#00ff00');
      }
      // 如果有游戏状态，立即更新界面
      if (this.gameState) {
        this.updateGameStateUI();
        this.updateOtherPlayersUI();
      }
    }
  }

  /**
   * createUI 创建游戏用户界面
   */
  createUI() {
    const { width } = this.scale;

    // 标题
    this.add.text(width / 2, 50, '财富自由之路', {
      fontSize: '32px',
      color: '#ffffff',
    }).setOrigin(0.5);

    // 玩家信息标题
    const playerName = this.currentUserInfo?.userName || '未知玩家';
    this.add.text(20, 20, `玩家: ${playerName}`, {
      fontSize: '24px',
      color: '#ffffff',
      fontStyle: 'bold'
    });

    // 连接状态
    this.uiElements.connectionStatus = this.add.text(20, 60, '连接中...', {
      fontSize: '16px',
      color: '#ffff00',
    });

    // 当前玩家姓名显示（更突出的位置）
    this.uiElements.currentPlayerName = this.add.text(width / 2, 90, '', {
      fontSize: '20px',
      color: '#00ff88',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 玩家信息区域
    this.uiElements.playerName = this.add.text(20, 100, '玩家: 未连接', {
      fontSize: '18px',
      color: '#ffffff',
    });

    this.uiElements.cash = this.add.text(20, 130, '现金: $0', {
      fontSize: '16px',
      color: '#00ff00',
    });

    this.uiElements.cashFlow = this.add.text(20, 160, '现金流: $0/月', {
      fontSize: '16px',
      color: '#00ff00',
    });

    // 游戏状态区域
    this.uiElements.gamePhase = this.add.text(20, 220, '游戏阶段: 等待中', {
      fontSize: '16px',
      color: '#ffffff',
    });

    this.uiElements.currentMonth = this.add.text(20, 250, '当前月份: 1', {
      fontSize: '16px',
      color: '#ffffff',
    });

    // 操作按钮区域
    this.add.text(width - 200, 100, '加入游戏', {
      fontSize: '18px',
      color: '#ffffff',
      backgroundColor: '#0066cc',
      padding: { x: 10, y: 5 }
    }).setInteractive().on('pointerdown', () => this.joinGame());

    this.add.text(width - 200, 150, '开始游戏', {
      fontSize: '18px',
      color: '#ffffff',
      backgroundColor: '#00cc66',
      padding: { x: 10, y: 5 }
    }).setInteractive().on('pointerdown', () => this.startGame());

    // 财务报表区域
    this.add.text(20, 320, '收入:', {
      fontSize: '16px',
      color: '#00ff00',
    });

    this.uiElements.incomes = this.add.text(20, 350, '工资: $0', {
      fontSize: '14px',
      color: '#ffffff',
    });

    this.add.text(20, 420, '支出:', {
      fontSize: '16px',
      color: '#ff6666',
    });

    this.uiElements.expenses = this.add.text(20, 450, '生活费: $0', {
      fontSize: '14px',
      color: '#ffffff',
    });

    // 资产区域
    this.add.text(width / 2, 320, '资产:', {
      fontSize: '16px',
      color: '#ffcc00',
    });

    this.uiElements.assets = this.add.text(width / 2, 350, '暂无资产', {
      fontSize: '14px',
      color: '#ffffff',
    });

    // 其他玩家区域
    this.add.text(width - 250, 220, '房间内其他玩家:', {
      fontSize: '16px',
      color: '#88ccff',
    });

    // 创建其他玩家容器
    this.otherPlayersContainer = this.add.container(width - 250, 250);
  }


  /**
   * setupWebSocketHandlers 设置WebSocket消息处理
   * 
   * 调用场景:
   *   使用现有WebSocket连接时调用，重新设置消息处理回调
   * 
   * 主要逻辑:
   *   设置WebSocket的onmessage回调处理游戏消息
   */
  setupWebSocketHandlers() {
    if (!this.socket) return;

    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log('GameScene收到消息:', message);
      this.handleServerMessage(message);
    };
  }

  /**
   * connectWebSocket 方法，用于创建和管理 WebSocket 连接。
   */
  connectWebSocket() {
    // 更新UI显示用户信息
    if (this.currentUserInfo) {
      this.uiElements.playerName?.setText(`欢迎，${this.currentUserInfo.userName}！`);
    }
    
    // 创建一个新的 WebSocket 连接
    this.socket = new WebSocket('ws://localhost:9070/ws');

    // 连接成功时的回调
    this.socket.onopen = () => {
      console.log('WebSocket connection established.');
      this.isConnected = true;
      this.uiElements.connectionStatus.setText('已连接');
      this.uiElements.connectionStatus.setColor('#00ff00');
    };

    // 收到消息时的回调
    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log('Message from server: ', message);
      this.handleServerMessage(message);
    };

    // 连接关闭时的回调
    this.socket.onclose = () => {
      console.log('WebSocket connection closed.');
      this.isConnected = false;
      this.uiElements.connectionStatus.setText('连接断开');
      this.uiElements.connectionStatus.setColor('#ff0000');
    };

    // 发生错误时的回调
    this.socket.onerror = (error) => {
      console.error('WebSocket error: ', error);
      this.uiElements.connectionStatus.setText('连接错误');
      this.uiElements.connectionStatus.setColor('#ff0000');
    };
  }

  /**
   * handleServerMessage 处理服务器消息
   * 
   * 调用场景:
   *   收到WebSocket消息时调用，根据新的消息分类系统处理不同类型的消息
   * 
   * 主要逻辑:
   *   按照6大类消息类型进行分类处理
   */
  handleServerMessage(message: any) {
    switch (message.type) {
      // ===== 1. 连接状态类消息 =====
      case 'connection.connect':
        console.log('已连接到服务器');
        break;
      case 'connection.disconnect':
        console.log('连接已断开');
        this.isConnected = false;
        this.uiElements.connectionStatus?.setText('连接断开');
        this.uiElements.connectionStatus?.setColor('#ff0000');
        break;
      case 'connection.heartbeat':
        // 心跳响应，保持静默
        break;

      // ===== 2. 房间管理类消息 =====
      case 'room.state':
        this.gameState = message.data;
        this.updateGameStateUI();
        this.updateOtherPlayersUI();
        break;
      case 'room.join':
        console.log('房间加入响应:', message.data);
        break;
      case 'room.leave':
        console.log('房间离开响应:', message.data);
        break;

      // ===== 3. 游戏管理类消息 =====
      case 'game.start':
        console.log('游戏开始');
        break;
      case 'game.state':
        this.gameState = message.data;
        this.updateGameStateUI();
        this.updateOtherPlayersUI();
        break;
      case 'game.pause':
        console.log('游戏暂停');
        break;
      case 'game.end':
        console.log('游戏结束');
        break;

      // ===== 4. 游戏内玩家操作类消息 =====
      case 'player.roll_dice':
        console.log('投掷骰子请求');
        break;
      case 'player.make_choice':
        console.log('玩家选择');
        break;
      case 'player.buy_asset':
        console.log('购买资产');
        break;
      case 'player.chat':
        this.handleChatMessage(message);
        break;

      // ===== 5. 服务器响应类消息 =====
      case 'server.dice_result':
        this.handleDiceResult(message.data);
        break;
      case 'server.move_result':
        this.handleMoveResult(message.data);
        break;
      case 'server.event_triggered':
        this.handleEventTriggered(message.data);
        break;
      case 'server.choice_required':
        this.handleChoiceRequired(message.data);
        break;
      case 'server.action_result':
        this.handleActionResult(message.data);
        break;
      case 'server.player_update':
        this.handlePlayerUpdate(message.data);
        break;
      case 'server.turn_changed':
        this.handleTurnChanged(message.data);
        break;
      case 'server.phase_changed':
        this.handlePhaseChanged(message.data);
        break;
      case 'server.payday_processed':
        console.log('发薪日处理完成');
        break;

      // ===== 6. 游戏内系统事件类消息 =====
      case 'system.event':
        this.handleSystemEvent(message.data);
        break;
      case 'system.black_swan':
        this.handleBlackSwanEvent(message.data);
        break;
      case 'system.market_crash':
        this.handleMarketCrash(message.data);
        break;
      case 'system.economic_boom':
        this.handleEconomicBoom(message.data);
        break;
      case 'system.inflation':
        this.handleInflationChange(message.data);
        break;
      case 'system.interest_rate':
        this.handleInterestChange(message.data);
        break;
      case 'system.news':
        this.handleNewsEvent(message.data);
        break;

      // ===== 通用消息类型 =====
      case 'success':
        console.log('操作成功:', message.data.message);
        if (message.data.data && message.data.data.player) {
          this.playerData = message.data.data.player;
          this.updatePlayerUI();
        }
        break;
      case 'error':
        console.error('服务器错误:', message.data.message);
        break;
      case 'info':
        console.log('服务器信息:', message.data.message);
        break;
      case 'warning':
        console.warn('服务器警告:', message.data.message);
        break;

      default:
        console.log('未知消息类型:', message.type, message.data);
        break;
    }
  }

  /**
   * joinGame 加入游戏房间
   */
  joinGame() {
    if (!this.isConnected || !this.socket) return;

    const message = {
      type: 'player.roll_dice',
      player_id: this.currentUserInfo?.userId || '',
      data: {
        user_id: this.currentUserInfo?.userId || '',
        user_name: this.currentUserInfo?.userName || '',
        dice_count: 1
      }
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * startGame 开始游戏
   */
  startGame() {
    if (!this.isConnected || !this.socket) return;

    const message = {
      type: 'game.start',
      player_id: this.currentUserInfo?.userId || '',
      data: {}
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * updatePlayerUI 更新玩家界面
   */
  updatePlayerUI() {
    if (!this.playerData) return;

    this.uiElements.playerName.setText(`玩家: ${this.playerData.name}`);
    this.uiElements.cash.setText(`现金: $${this.playerData.cash}`);
    this.uiElements.cashFlow.setText(`现金流: $${this.playerData.cash_flow}/月`);

    // 更新收入信息
    const incomes = this.playerData.incomes || [];
    const incomeText = incomes.map((income: any) => 
      `${income.source}: $${income.amount}`
    ).join('\n') || '暂无收入';
    this.uiElements.incomes.setText(incomeText);

    // 更新支出信息
    const expenses = this.playerData.expenses || [];
    const expenseText = expenses.map((expense: any) => 
      `${expense.description}: $${expense.amount}`
    ).join('\n') || '暂无支出';
    this.uiElements.expenses.setText(expenseText);

    // 更新资产信息
    const assets = this.playerData.assets || [];
    const assetText = assets.map((asset: any) => 
      `${asset.name}: $${asset.current_value} (收入: $${asset.monthly_income})`
    ).join('\n') || '暂无资产';
    this.uiElements.assets.setText(assetText);
  }

  /**
   * updateGameStateUI 更新游戏状态界面
   */
  updateGameStateUI() {
    if (!this.gameState) return;

    const phaseNames: { [key: string]: string } = {
      'waiting': '等待中',
      'started': '已开始',
      'payday': '发薪日',
      'market': '市场机会',
      'doodad': '小支出',
      'finished': '游戏结束'
    };

    this.uiElements.gamePhase.setText(`游戏阶段: ${phaseNames[this.gameState.phase] || this.gameState.phase}`);
    this.uiElements.currentMonth.setText(`当前月份: ${this.gameState.current_month}`);
  }

  /**
   * updateOtherPlayersUI 更新其他玩家显示
   * 
   * 调用场景:
   *   收到游戏状态更新时，显示房间内其他玩家信息
   * 
   * 主要逻辑:
   *   遍历游戏状态中的玩家列表，排除当前玩家，显示其他玩家姓名
   */
  updateOtherPlayersUI() {
    if (!this.gameState || !this.otherPlayersContainer) return;

    // 清除之前的显示
    this.otherPlayersContainer.removeAll(true);

    // 获取其他玩家列表（排除当前玩家）
    const otherPlayers = Object.values(this.gameState.players || {}).filter(
      (player: any) => player.name !== this.currentUserInfo?.userName
    );

    // 显示其他玩家
    otherPlayers.forEach((player: any, index: number) => {
      const playerText = this.add.text(0, index * 25, `• ${player.name}`, {
        fontSize: '14px',
        color: '#88ccff',
      });
      this.otherPlayersContainer!.add(playerText);
    });

    // 如果没有其他玩家，显示提示
    if (otherPlayers.length === 0) {
      const emptyText = this.add.text(0, 0, '暂无其他玩家', {
        fontSize: '14px',
        color: '#666666',
      });
      this.otherPlayersContainer.add(emptyText);
    }
  }

  // ===== 新增消息处理方法 =====

  /**
   * handleChatMessage 处理聊天消息
   */
  private handleChatMessage(message: any) {
    const chatData = message.data;
    console.log(`[${chatData.timestamp}] 玩家聊天: ${chatData.message}`);
    // 这里可以添加聊天界面显示逻辑
  }

  /**
   * handleDiceResult 处理骰子结果
   */
  private handleDiceResult(data: any) {
    console.log(`骰子结果: ${data.dice_values.join(', ')}, 总步数: ${data.total_steps}`);
    // 这里可以添加骰子动画和结果显示
  }

  /**
   * handleMoveResult 处理移动结果
   */
  private handleMoveResult(data: any) {
    console.log(`玩家 ${data.player_id} 从格子 ${data.from_square} 移动到 ${data.to_square}`);
    // 这里可以添加玩家移动动画
  }

  /**
   * handleEventTriggered 处理触发事件
   */
  private handleEventTriggered(data: any) {
    console.log(`触发事件: ${data.title} - ${data.description}`);
    // 这里可以添加事件弹窗显示
  }

  /**
   * handleChoiceRequired 处理需要玩家选择
   */
  private handleChoiceRequired(data: any) {
    console.log('需要玩家做出选择:', data);
    // 这里可以添加选择界面
  }

  /**
   * handleActionResult 处理操作结果
   */
  private handleActionResult(data: any) {
    console.log(`操作结果: ${data.message} (${data.success ? '成功' : '失败'})`);
    // 这里可以添加操作结果提示
  }

  /**
   * handlePlayerUpdate 处理玩家状态更新
   */
  private handlePlayerUpdate(data: any) {
    // 更新玩家数据并刷新界面
    this.playerData = data;
    this.updatePlayerUI();
  }

  /**
   * handleTurnChanged 处理回合切换
   */
  private handleTurnChanged(data: any) {
    console.log(`回合切换: 当前玩家 ${data.current_player_id}, 回合 ${data.turn_number}`);
    // 这里可以添加回合切换提示
  }

  /**
   * handlePhaseChanged 处理阶段切换
   */
  private handlePhaseChanged(data: any) {
    console.log(`阶段切换: ${data.previous_phase} -> ${data.current_phase}`);
    // 这里可以添加阶段切换动画
  }

  /**
   * handleSystemEvent 处理系统事件
   */
  private handleSystemEvent(data: any) {
    console.log(`系统事件: ${data.title} - ${data.description}`);
    // 这里可以添加系统事件通知
  }

  /**
   * handleBlackSwanEvent 处理黑天鹅事件
   */
  private handleBlackSwanEvent(data: any) {
    console.log(`黑天鹅事件: ${data.title} - 影响程度: ${data.impact_level}`);
    // 这里可以添加黑天鹅事件特效
  }

  /**
   * handleMarketCrash 处理市场崩盘
   */
  private handleMarketCrash(data: any) {
    console.log(`市场崩盘事件: 强度 ${data.intensity}, 持续 ${data.duration} 个月`);
    // 这里可以添加市场崩盘特效
  }

  /**
   * handleEconomicBoom 处理经济繁荣
   */
  private handleEconomicBoom(data: any) {
    console.log(`经济繁荣事件: 强度 ${data.intensity}, 持续 ${data.duration} 个月`);
    // 这里可以添加经济繁荣特效
  }

  /**
   * handleInflationChange 处理通胀变化
   */
  private handleInflationChange(data: any) {
    console.log(`通胀率变化: ${data.inflation_rate}%`);
    // 这里可以添加通胀变化提示
  }

  /**
   * handleInterestChange 处理利率变化
   */
  private handleInterestChange(data: any) {
    console.log(`利率变化: ${data.interest_rate}%`);
    // 这里可以添加利率变化提示
  }

  /**
   * handleNewsEvent 处理新闻事件
   */
  private handleNewsEvent(data: any) {
    console.log(`新闻事件: ${data.title}`);
    // 这里可以添加新闻滚动显示
  }

  // ===== 新增玩家操作方法 =====

  /**
   * rollDice 投掷骰子
   */
  rollDice(diceCount: number = 1) {
    if (!this.isConnected || !this.socket) return;

    const message = {
      type: 'player.roll_dice',
      player_id: this.currentUserInfo?.userId || '',
      data: {
        user_id: this.currentUserInfo?.userId || '',
        user_name: this.currentUserInfo?.userName || '',
        dice_count: diceCount
      }
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * sendChatMessage 发送聊天消息
   */
  sendChatMessage(message: string) {
    if (!this.isConnected || !this.socket || !message.trim() || !this.currentUserInfo) return;

    const chatMessage = {
      type: 'player.chat',
      player_id: this.currentUserInfo.userId,
      data: {
        message: message,
        timestamp: new Date().toLocaleTimeString(),
        is_public: true,
        user_id: this.currentUserInfo.userId,
        user_name: this.currentUserInfo.userName
      }
    };

    this.socket.send(JSON.stringify(chatMessage));
  }

  /**
   * sendHeartbeat 发送心跳
   */
  sendHeartbeat() {
    if (!this.isConnected || !this.socket) return;

    const message = {
      type: 'player.buy_asset',
      player_id: this.currentUserInfo?.userId || '',
      data: {
        message: 'ping'
      }
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * update 方法，每一帧都会调用。
   *
   * 调用场景:
   *   create 方法执行完毕后，由 Phaser 引擎在每一帧循环调用。
   *
   * 主要逻辑:
   *   定期发送心跳，保持连接活跃
   */
  update() {
    // 每30秒发送一次心跳
    const now = Date.now();
    if (!this.lastHeartbeat || now - this.lastHeartbeat > 30000) {
      this.sendHeartbeat();
      this.lastHeartbeat = now;
    }
  }

  private lastHeartbeat: number = 0;
}
