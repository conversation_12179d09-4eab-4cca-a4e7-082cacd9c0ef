import Phaser from 'phaser';
import { UserManager } from '../utils/UserManager';
import type { Account, AccountStatus } from '../types/Account';

/**
 * MainMenuScene 是游戏的主菜单场景
 * 
 * 调用场景:
 *   游戏启动时的第一个场景，用于显示玩家信息、房间列表和开始游戏
 * 
 * 主要功能:
 *   - 显示玩家基本信息
 *   - 显示当前房间列表
 *   - 提供开始游戏按钮
 *   - 处理房间查找和创建逻辑
 */
export class MainMenuScene extends Phaser.Scene {
  private socket?: WebSocket;
  private isConnected = false;
  private currentAccount: Account | null = null;
  private uiElements: { [key: string]: Phaser.GameObjects.Text | Phaser.GameObjects.Container } = {};
  private roomListRefreshTimer?: Phaser.Time.TimerEvent;

  /**
   * 构造函数，设置场景的key
   */
  constructor() {
    super('MainMenuScene');
  }

  /**
   * preload 方法，预加载资源
   */
  preload() {
    // 预加载游戏资源
  }

  /**
   * create 方法，创建主菜单界面
   * 
   * 调用场景:
   *   场景启动时自动调用
   * 
   * 主要逻辑:
   *   初始化用户信息，创建主菜单UI界面并连接WebSocket
   */
  create() {
    this.initializeUser();
    this.createUI();
    this.connectWebSocket();
  }

  /**
   * initializeUser 初始化用户信息
   * 
   * 调用场景:
   *   场景创建时调用，获取用户信息并转换为Account模型
   * 
   * 主要逻辑:
   *   使用UserManager获取用户信息，直接转换为Account模型格式
   */
  private initializeUser() {
    const userInfo = UserManager.getUserInfo();
    
    // 将UserInfo转换为Account模型
    if (userInfo) {
      this.currentAccount = {
        id: userInfo.userId,
        username: userInfo.userName,
        nickname: userInfo.userName,
        status: 'online' as AccountStatus,
        statistics: {
          total_games: 0,
          win_games: 0,
          best_cash_flow: 0,
          average_game_time: 0,
          last_played_at: new Date().toISOString()
        },
        last_login_at: new Date(userInfo.lastLoginAt).toISOString(),
        created_at: new Date(userInfo.createdAt).toISOString(),
        updated_at: new Date().toISOString()
      };
    }
    
    console.log('账号信息初始化完成:', this.currentAccount);
  }

  /**
   * createUI 创建主菜单用户界面
   * 
   * 调用场景:
   *   create方法中调用，用于构建主菜单界面
   * 
   * 主要逻辑:
   *   创建标题、玩家信息显示、连接状态、开始游戏按钮和房间列表
   */
  createUI() {
    const { width, height } = this.scale;

    // 游戏标题
    this.add.text(width / 2, 100, '财富自由之路', {
      fontSize: '48px',
      color: '#ffffff',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 副标题
    this.add.text(width / 2, 160, '现金流游戏 - 在线多人版', {
      fontSize: '24px',
      color: '#cccccc'
    }).setOrigin(0.5);

    // 连接状态
    this.uiElements.connectionStatus = this.add.text(width / 2, 220, '连接中...', {
      fontSize: '18px',
      color: '#ffff00'
    }).setOrigin(0.5);

    // 账号信息区域
    this.add.text(width / 2, 300, '账号信息', {
      fontSize: '28px',
      color: '#00ff88',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 当前账号昵称
    this.uiElements.accountName = this.add.text(width / 2, 350, '', {
      fontSize: '24px',
      color: '#ffffff'
    }).setOrigin(0.5);

    // 账号状态
    this.uiElements.accountStatus = this.add.text(width / 2, 390, '状态: 在线', {
      fontSize: '20px',
      color: '#00ff88'
    }).setOrigin(0.5);

    // 游戏统计信息
    this.uiElements.gameStats = this.add.text(width / 2, 430, '总游戏局数: 0 | 胜率: 0%', {
      fontSize: '18px',
      color: '#cccccc'
    }).setOrigin(0.5);

    // 开始游戏按钮
    const startButton = this.add.text(width / 2, 500, '开始游戏', {
      fontSize: '32px',
      color: '#ffffff',
      backgroundColor: '#0066cc',
      padding: { x: 30, y: 15 }
    }).setOrigin(0.5).setInteractive();

    startButton.on('pointerdown', () => this.startGame());
    startButton.on('pointerover', () => {
      startButton.setStyle({ backgroundColor: '#0088ff' });
    });
    startButton.on('pointerout', () => {
      startButton.setStyle({ backgroundColor: '#0066cc' });
    });

    // 房间列表标题
    this.add.text(width / 2, 560, '当前房间列表', {
      fontSize: '24px',
      color: '#00ff88',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // 房间列表容器
    this.uiElements.roomListContainer = this.add.container(width / 2, 600);

    // 游戏说明
    this.add.text(width / 2, height - 80, '点击开始游戏将自动匹配房间或创建新房间', {
      fontSize: '16px',
      color: '#888888'
    }).setOrigin(0.5);

    // 版本信息
    this.add.text(20, height - 30, 'v0.2.0 - 现金流游戏', {
      fontSize: '14px',
      color: '#666666'
    });
  }


  /**
   * connectWebSocket 连接WebSocket服务器
   * 
   * 调用场景:
   *   create方法中调用，建立与服务器的连接
   * 
   * 主要逻辑:
   *   生成随机玩家姓名，建立WebSocket连接，设置消息处理回调
   */
  connectWebSocket() {
    // 更新UI显示账号信息
    if (this.currentAccount) {
      this.uiElements.accountName?.setText(`欢迎，${this.currentAccount.nickname}！`);
      
      // 更新账号状态
      this.uiElements.accountStatus?.setText(`状态: ${this.currentAccount.status}`);
      
      // 更新游戏统计
      const stats = this.currentAccount.statistics;
      const winRate = stats.total_games > 0 ? Math.round((stats.win_games / stats.total_games) * 100) : 0;
      this.uiElements.gameStats?.setText(`总游戏局数: ${stats.total_games} | 胜率: ${winRate}%`);
    }
    
    // 创建WebSocket连接
    this.socket = new WebSocket('ws://localhost:9070/ws');

    // 连接成功回调
    this.socket.onopen = () => {
      console.log('WebSocket连接已建立');
      this.isConnected = true;
      this.uiElements.connectionStatus.setText('已连接到服务器');
      this.uiElements.connectionStatus.setColor('#00ff00');
      
      // 发送连接消息，携带账号信息
      if (this.currentAccount && this.socket) {
        const connectMessage = {
          type: 'connection.connect',
          player_id: this.currentAccount.id,
          data: {
            user_id: this.currentAccount.id,
            user_name: this.currentAccount.nickname,
            account: this.currentAccount
          }
        };
        this.socket.send(JSON.stringify(connectMessage));
      }

      // 获取房间列表
      this.requestRoomList();
      
      // 设置定时器定期刷新房间列表（每5秒）
      this.roomListRefreshTimer = this.time.addEvent({
        delay: 5000,
        callback: () => this.requestRoomList(),
        loop: true
      });
    };

    // 收到消息回调
    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log('收到服务器消息:', message);
      this.handleServerMessage(message);
    };

    // 连接关闭回调
    this.socket.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.isConnected = false;
      this.uiElements.connectionStatus.setText('连接已断开');
      this.uiElements.connectionStatus.setColor('#ff0000');
    };

    // 连接错误回调
    this.socket.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      this.uiElements.connectionStatus.setText('连接错误');
      this.uiElements.connectionStatus.setColor('#ff0000');
    };
  }

  /**
   * handleServerMessage 处理服务器消息
   * 
   * 调用场景:
   *   收到WebSocket消息时调用
   * 
   * 主要逻辑:
   *   根据消息类型处理不同的服务器响应
   */
  handleServerMessage(message: any) {
    switch (message.type) {
      case 'connection.connect':
        console.log('服务器连接确认');
        break;
      
      case 'game.rejoin':
        // 服务器通知用户需要重新加入游戏
        console.log('检测到用户正在游戏中，准备重新加入:', message.data);
        this.rejoinGame(message.data);
        break;
      
      case 'room.list':
        // 处理房间列表响应
        this.handleRoomList(message.data);
        break;
      
      case 'game.joinOrCreate':
        // 处理加入或创建房间的响应
        console.log('收到加入或创建房间的响应:', message.data);
        if (message.data && message.data.room_id) {
          // 成功获取到房间信息，切换到房间场景
          this.switchToRoomScene(message.data);
        } else {
          // 处理错误情况
          console.error('加入或创建房间失败:', message.data);
          this.uiElements.connectionStatus.setText(`错误: ${message.data.message || '加入或创建房间失败'}`);
          this.uiElements.connectionStatus.setColor('#ff0000');
        }
        break;
      
      case 'room.join':
        // 加入房间成功，切换到房间场景
        console.log('成功加入房间:', message.data);
        // 直接切换到房间场景，不等待room.state消息
        this.switchToRoomScene(message.data.data || message.data);
        break;
      
      case 'room.create':
        // 创建房间成功，切换到房间场景
        console.log('成功创建房间:', message.data);
        this.switchToRoomScene(message.data);
        break;
      
      case 'room.state':
        // 收到房间状态更新，切换到房间场景
        console.log('收到房间状态，切换到房间场景:', message.data);
        this.switchToRoomScene(message.data);
        break;
      
      case 'error':
        console.error('服务器错误:', message.data.message);
        this.uiElements.connectionStatus.setText(`错误: ${message.data.message}`);
        this.uiElements.connectionStatus.setColor('#ff0000');
        break;
      
      case 'success':
        console.log('操作成功:', message.data.message);
        break;
      
      default:
        console.log('未知消息类型:', message.type);
        break;
    }
  }

  /**
   * startGame 开始游戏逻辑
   * 
   * 调用场景:
   *   点击开始游戏按钮时调用
   * 
   * 主要逻辑:
   *   向服务器发送加入或创建房间的请求，由服务器决定是加入现有房间还是创建新房间
   */
  startGame() {
    if (!this.isConnected || !this.socket) {
      console.log('未连接到服务器');
      return;
    }

    console.log('请求加入或创建房间...');
    this.uiElements.connectionStatus.setText('正在查找或创建房间...');
    this.uiElements.connectionStatus.setColor('#ffff00');

    // 发送加入或创建房间请求
    const message = { 
      type: 'game.joinOrCreate',
      player_id: this.currentAccount?.id || '',
      data: {
        account: this.currentAccount
      }
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * requestRoomList 请求房间列表
   * 
   * 调用场景:
   *   WebSocket连接成功后调用，获取所有房间列表
   *   定时刷新时调用
   * 
   * 主要逻辑:
   *   发送房间列表查询请求
   */
  requestRoomList() {
    if (!this.isConnected || !this.socket) {
      console.log('未连接到服务器');
      return;
    }

    console.log('请求房间列表...');
    
    // 发送房间列表查询请求（获取所有房间，不只可用的）
    const message = { 
      type: 'room.list',
      player_id: this.currentAccount?.id || '',
      data: {
        only_available: false // 获取所有房间
      }
    };

    this.socket.send(JSON.stringify(message));
  }

  /**
   * handleRoomList 处理房间列表响应
   * 
   * 调用场景:
   *   收到房间列表消息时调用
   * 
   * 主要逻辑:
   *   显示房间列表，并处理开始游戏的房间查找逻辑
   */
  handleRoomList(roomListData: any) {
    const rooms = roomListData.rooms || [];
    
    // 更新房间列表显示
    this.updateRoomListDisplay(rooms);
    
    // 如果是开始游戏的请求（只查找可用房间），则继续原来的逻辑
    if (roomListData.request_type === 'start_game') {
      const availableRooms = rooms.filter((room: any) => 
        !room.is_started && Object.keys(room.accounts).length < room.max_players
      );
      
      if (availableRooms.length > 0) {
        // 有可用房间，加入第一个
        const room = availableRooms[0];
        console.log('找到可用房间，正在加入:', room.room_id);
        this.joinRoom(room.room_id);
      } else {
        // 没有可用房间，创建新房间
        console.log('没有可用房间，正在创建新房间');
        this.createRoom();
      }
    }
  }

  /**
   * updateRoomListDisplay 更新房间列表显示
   * 
   * 调用场景:
   *   收到房间列表时调用
   * 
   * 主要逻辑:
   *   清除旧的房间列表，创建新的房间列表显示
   */
  updateRoomListDisplay(rooms: any[]) {
    // 清除旧的房间列表
    this.uiElements.roomListContainer?.destroy(true);
    
    const { width } = this.scale;
    const roomListContainer = this.add.container(width / 2, 600);
    this.uiElements.roomListContainer = roomListContainer;
    
    if (rooms.length === 0) {
      const noRoomText = this.add.text(0, 0, '暂无房间', {
        fontSize: '18px',
        color: '#888888'
      }).setOrigin(0.5);
      roomListContainer.add(noRoomText);
      return;
    }
    
    // 显示最多5个房间
    const displayRooms = rooms.slice(0, 5);
    const itemHeight = 30;
    
    displayRooms.forEach((room, index) => {
      const y = index * itemHeight;
      const playerCount = Object.keys(room.accounts || {}).length;
      const status = room.is_started ? '游戏中' : '等待中';
      const roomText = `[${room.room_id}] ${room.room_name} (${playerCount}/${room.max_players}) ${status}`;
      
      const roomTextObj = this.add.text(0, y, roomText, {
        fontSize: '16px',
        color: room.is_started ? '#ff8800' : '#00ff88'
      }).setOrigin(0.5);
      
      roomListContainer.add(roomTextObj);
    });
    
    // 如果有更多房间，显示提示
    if (rooms.length > 5) {
      const moreText = this.add.text(0, 5 * itemHeight, `... 还有 ${rooms.length - 5} 个房间`, {
        fontSize: '14px',
        color: '#888888'
      }).setOrigin(0.5);
      roomListContainer.add(moreText);
    }
  }

  /**
   * joinRoom 加入指定房间
   * 
   * 调用场景:
   *   找到可用房间时调用
   * 
   * 主要逻辑:
   *   发送加入房间请求
   */
  joinRoom(roomId: string) {
    if (!this.socket) return;

    const message = {
      type: 'room.join',
      player_id: this.currentAccount?.id || '',
      data: {
        account_id: this.currentAccount?.id || '',
        account: this.currentAccount,
        room_id: roomId
      }
    };

    this.socket.send(JSON.stringify(message));
    this.uiElements.connectionStatus.setText('正在加入房间...');
  }

  /**
   * createRoom 创建新房间
   * 
   * 调用场景:
   *   没有可用房间时调用
   * 
   * 主要逻辑:
   *   发送创建房间请求
   */
  createRoom() {
    if (!this.socket) return;

    const roomId = `room_${Date.now()}`;
    const message = {
      type: 'room.create',
      player_id: this.currentAccount?.id || '',
      data: {
        room_name: `${this.currentAccount?.nickname || '玩家'}的房间`,
        room_id: roomId,
        max_players: 4,
        is_private: false,
        game_mode: 'classic',
        difficulty: 'normal',
        account: this.currentAccount
      }
    };

    this.socket.send(JSON.stringify(message));
    this.uiElements.connectionStatus.setText('正在创建房间...');
  }

  /**
   * switchToRoomScene 切换到房间场景
   * 
   * 调用场景:
   *   成功加入或创建房间后调用
   * 
   * 主要逻辑:
   *   传递WebSocket连接和玩家信息到房间场景
   */
  switchToRoomScene(roomData: any) {
    // 处理嵌套的数据结构，服务器可能返回 {data: {player: ..., room_id: ...}} 格式
    let actualRoomData = roomData;
    if (roomData.data && roomData.data.room_id) {
      actualRoomData = roomData.data;
    }
    
    // 构造房间数据，确保包含必要信息
    const roomInfo = {
      room_id: actualRoomData.room_id || actualRoomData.id || 'default_game',
      room_name: actualRoomData.room_name || actualRoomData.room_id || 'default_game',
      room_owner: actualRoomData.room_owner || this.currentAccount?.nickname || '未知玩家',
      accounts: actualRoomData.accounts || actualRoomData.players || {},
      max_players: actualRoomData.max_players || 4,
      is_started: actualRoomData.is_started || false
    };

    console.log('切换到房间场景，传递数据:', roomInfo);

    // 传递数据到房间场景，使用Account模型
    this.scene.start('RoomScene', {
      socket: this.socket,
      account: this.currentAccount, // 传递Account对象
      roomData: roomInfo
    });
  }

  /**
   * rejoinGame 重新加入游戏
   * 
   * 调用场景:
   *   服务器检测到用户正在游戏中时调用
   * 
   * 主要逻辑:
   *   直接跳转到GameScene恢复游戏状态
   */
  rejoinGame(gameData: any) {
    console.log('重新加入游戏:', gameData);
    
    // 直接切换到游戏场景
    this.scene.start('GameScene', {
      socket: this.socket,
      account: this.currentAccount,
      gameData: gameData,
      isRejoining: true
    });
  }

  /**
   * shutdown 清理资源
   * 
   * 调用场景:
   *   场景销毁时调用
   * 
   * 主要逻辑:
   *   清理定时器等资源
   */
  shutdown() {
    if (this.roomListRefreshTimer) {
      this.roomListRefreshTimer.remove();
      this.roomListRefreshTimer = undefined;
    }
  }
}
