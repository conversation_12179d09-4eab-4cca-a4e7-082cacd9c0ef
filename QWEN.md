# 现金流游戏 (CashFlow Game) 项目上下文

## 项目概述

这是一个基于《富爸爸穷爸爸》现金流游戏的在线多人版本，采用前后端分离架构，支持实时多人对战。项目使用现代Web技术栈构建，具有清晰的架构设计和良好的可扩展性。

### 核心功能
- **现金流管理**：模拟真实的收入、支出和现金流计算
- **资产投资**：购买股票、房地产、企业等投资资产
- **被动收入**：通过资产获得持续的被动收入
- **多人游戏**：支持最多4人同时游戏的实时房间系统
- **实时通信**：基于WebSocket的低延迟实时交互

### 技术架构
```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   前端 (Web)    │ ←──────────────→ │   后端 (Go)     │
│                 │                 │                 │
│ • Phaser.js     │                 │ • HTTP Server   │
│ • TypeScript    │                 │ • WebSocket     │
│ • Vite          │                 │ • Game Logic    │
│ • pnpm          │                 │ • Clean Arch    │
└─────────────────┘                 └─────────────────┘
```

## 项目结构

```
cashfree/
├── server/                 # 后端Go项目
│   ├── model/              # 数据模型层
│   │   ├── player.go       # 玩家模型
│   │   ├── game.go         # 游戏状态模型
│   │   └── message.go      # 消息通信模型
│   ├── main.go             # 服务器入口
│   └── go.mod              # Go模块配置
├── webgame/                # 前端游戏项目
│   ├── src/
│   │   ├── scenes/         # 游戏场景
│   │   │   └── GameScene.ts
│   │   └── main.ts         # 游戏入口
│   ├── vite.config.ts      # Vite配置
│   ├── tsconfig.json       # TypeScript配置
│   └── package.json        # 前端依赖
├── README.md               # 项目文档
└── DOCS.md                 # 详细技术文档
```

## 后端 (Go)

### 技术栈
- **语言**：Go 1.25.0
- **Web框架**：net/http (Go标准库)
- **WebSocket**：gorilla/websocket v1.5.3
- **架构模式**：Clean Architecture

### 核心组件

#### 1. 数据模型 (model/)
- `Player`：玩家模型，包含财务状况(现金、现金流、资产、收入、支出)
- `GameState`：游戏状态模型，管理房间、玩家列表、游戏阶段
- `Message`：消息通信模型，定义6大类消息类型系统

#### 2. 服务器入口 (main.go)
- HTTP服务监听在8080端口
- WebSocket端点：`/ws`
- 健康检查端点：`/health`
- 游戏管理器：`GameManager`负责管理所有游戏房间和玩家连接

#### 3. 消息处理系统
采用6大类消息分类体系：
1. **连接状态类** (`connection.*`)：连接、断开、心跳
2. **房间管理类** (`room.*`)：加入、创建、离开房间
3. **游戏管理类** (`game.*`)：开始、暂停、结束游戏
4. **玩家操作类** (`player.*`)：投掷骰子、购买资产、聊天
5. **服务器响应类** (`server.*`)：骰子结果、操作结果、状态更新
6. **系统事件类** (`system.*`)：黑天鹅事件、经济变化

## 前端 (TypeScript + Phaser)

### 技术栈
- **游戏引擎**：Phaser.js 3.90.0
- **开发语言**：TypeScript 5.8.3
- **构建工具**：Vite 7.1.4
- **包管理器**：pnpm
- **通信协议**：WebSocket

### 核心组件

#### 1. 游戏场景 (scenes/)
- `MainMenuScene`：主菜单场景，处理用户连接和房间匹配
- `RoomScene`：房间管理场景，显示玩家列表和房间状态
- `GameScene`：主游戏场景，处理游戏逻辑和UI显示

#### 2. 用户管理 (utils/)
- `UserManager`：用户信息管理，生成唯一ID和随机中文姓名

## 开发与运行

### 环境要求
- Go 1.25.0+
- Node.js 18+
- pnpm 8+

### 启动命令
```bash
# 启动后端
cd server && go run main.go

# 启动前端
cd webgame && pnpm install && pnpm run dev
```

### 默认端口
- **后端HTTP服务**：http://localhost:9070
- **WebSocket服务**：ws://localhost:9070/ws
- **前端开发服务**：http://localhost:5177

## 开发约定

### 代码规范
- **Go代码**：遵循gofmt格式化，添加详细中文注释
- **TypeScript**：严格类型检查，ESLint代码规范
- **Git提交**：使用中文commit message，描述具体变更

### 添加新功能流程
1. 后端：在`model/`中定义数据结构，在`main.go`中添加处理逻辑
2. 前端：在相应Scene中添加UI和交互逻辑
3. 通信：在`message.go`中定义新的消息类型
4. 测试：确保前后端功能正常

### 调试技巧
- 后端日志：查看控制台输出
- 前端调试：使用浏览器开发者工具
- WebSocket：监控Network面板的WS连接

## 项目状态

当前版本：v0.2.0 (消息系统重构版)

已完成功能：
- ✅ 基础架构搭建
- ✅ WebSocket实时通信
- ✅ 核心游戏循环
- ✅ 财务系统计算
- ✅ 多人房间管理
- ✅ 基础UI界面
- ✅ 消息分类系统重构

技术指标：
- 后端性能：支持并发连接，内存安全
- 前端体验：60FPS流畅运行，响应式设计
- 通信延迟：< 50ms WebSocket响应
- 代码质量：TypeScript类型安全，Go简洁架构