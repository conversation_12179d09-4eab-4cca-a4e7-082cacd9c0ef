# 现金流游戏项目文档

## 📋 项目概述

### 产品定位
基于《富爸爸穷爸爸》现金流游戏的在线多人版本，采用前后端分离架构，支持实时多人对战。

### 核心价值
- **教育意义**：通过游戏学习财务管理和投资理念
- **社交互动**：多人实时对战，增强游戏体验
- **技术创新**：现代Web技术栈，高性能实时通信

## 🎮 产品功能

### 已实现功能
- ✅ **现金流管理**：收入、支出、现金流实时计算
- ✅ **多人房间系统**：最多4人同时游戏
- ✅ **实时状态同步**：WebSocket低延迟通信
- ✅ **游戏循环**：发薪日 → 市场机会 → 小支出循环
- ✅ **财务报表界面**：实时显示玩家财务状况
- ✅ **基础资产系统**：资产购买和被动收入计算

### 规划功能
- 🔄 **角色职业系统**：不同起始条件和成长路径
- 🔄 **事件卡片系统**：市场机会、人生事件、意外支出
- 🔄 **动态经济系统**：通胀、利率变化、黑天鹅事件
- 🔄 **AI玩家系统**：单机模式电脑对手
- 🔄 **胜利条件判断**：财务自由目标达成检测

## 🏗️ 技术架构

### 整体架构图
```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   前端 (Web)    │ ←──────────────→ │   后端 (Go)     │
│                 │                 │                 │
│ • Phaser.js     │                 │ • HTTP Server   │
│ • TypeScript    │                 │ • WebSocket     │
│ • Vite          │                 │ • Game Logic    │
│ • pnpm          │                 │ • Clean Arch    │
└─────────────────┘                 └─────────────────┘
```

### 技术栈详情

#### 后端技术栈
- **语言**：Go 1.25.0 - 高性能、并发安全
- **Web框架**：net/http - Go标准库
- **WebSocket**：gorilla/websocket v1.5.3
- **架构模式**：Clean Architecture
- **并发控制**：sync.RWMutex读写锁

#### 前端技术栈
- **游戏引擎**：Phaser.js 3.90.0 - 2D游戏框架
- **开发语言**：TypeScript 5.8.3 - 类型安全
- **构建工具**：Vite 7.1.4 - 快速构建
- **包管理**：pnpm 10.15.0 - 高效包管理
- **通信**：WebSocket API - 实时双向通信

### 项目结构
```
cashfree/
├── server/                 # Go后端项目
│   ├── model/             # 数据模型层
│   │   ├── player.go      # 玩家模型 - 财务状况管理
│   │   ├── game.go        # 游戏状态模型 - 房间管理
│   │   └── message.go     # 消息模型 - 通信协议
│   ├── main.go            # 服务器入口 - HTTP/WebSocket服务
│   ├── go.mod             # Go模块依赖
│   └── go.sum             # 依赖版本锁定
├── webgame/               # 前端游戏项目
│   ├── src/
│   │   ├── scenes/        # Phaser游戏场景
│   │   │   └── GameScene.ts # 主游戏场景
│   │   └── main.ts        # 游戏入口点
│   ├── vite.config.ts     # Vite构建配置
│   ├── tsconfig.json      # TypeScript编译配置
│   └── package.json       # 前端依赖管理
└── README.md              # 项目说明文档
```

## 🎯 关键设计决策

### 1. Clean Architecture (简洁架构)
**设计原则**：
- **依赖倒置**：内层不依赖外层，通过接口解耦
- **单一职责**：每个模块职责明确，便于测试和维护
- **开闭原则**：对扩展开放，对修改关闭

**分层结构**：
- **模型层 (Model)**：纯业务逻辑，无外部依赖
- **服务层 (Service)**：业务用例实现
- **接口层 (Handler)**：HTTP/WebSocket处理
- **基础设施层 (Infrastructure)**：数据存储、外部服务

### 2. 实时状态同步机制
**事件驱动架构**：
- 基于WebSocket的双向通信
- 消息类型化设计，类型安全
- 状态变更自动广播给所有玩家

**并发安全设计**：
- 使用sync.RWMutex保护共享数据
- 连接生命周期安全管理
- 原子操作保证数据一致性

### 3. 可扩展性设计
**模块化结构**：
- 清晰的模块边界
- 插件化事件系统
- 配置驱动的游戏规则

## 🔄 核心流程

### 游戏启动流程
1. **客户端连接**：WebSocket握手建立连接
2. **玩家加入**：发送join_game消息创建玩家实例
3. **房间管理**：自动创建或加入默认游戏房间
4. **状态同步**：广播游戏状态给所有玩家
5. **游戏开始**：满足条件后启动游戏循环

### 游戏循环机制
```
等待阶段 → 发薪日阶段 → 市场机会阶段 → 小支出阶段 → 下个月
    ↑                                                    ↓
    ←←←←←←←←←←←←← 循环继续 ←←←←←←←←←←←←←←←←←←←←←←←←
```

### 现金流计算逻辑
1. **被动收入计算**：遍历所有资产的月收入
2. **总支出计算**：固定支出 + 变动支出
3. **现金流更新**：被动收入 - 总支出
4. **现金更新**：现金 + 现金流
5. **状态广播**：同步更新给所有玩家

## 📊 核心数据结构

### Player (玩家模型)
```go
type Player struct {
    ID         string    // 玩家唯一标识
    Name       string    // 玩家姓名
    Profession string    // 职业类型
    Cash       int       // 现金余额
    CashFlow   int       // 月现金流
    Assets     []Asset   // 资产列表
    Incomes    []Income  // 收入项目
    Expenses   []Expense // 支出项目
    CreatedAt  time.Time // 创建时间
    UpdatedAt  time.Time // 更新时间
}
```

**核心方法**：
- `CalculateCashFlow()` - 重新计算月现金流
- `AddAsset(asset Asset)` - 添加资产并更新收入

### GameState (游戏状态)
```go
type GameState struct {
    ID           string             // 房间唯一标识
    Players      map[string]*Player // 玩家映射表
    CurrentMonth int                // 当前游戏月份
    Phase        GamePhase          // 当前游戏阶段
    IsStarted    bool               // 游戏是否已开始
    MaxPlayers   int                // 最大玩家数量
    CreatedAt    time.Time          // 创建时间
    UpdatedAt    time.Time          // 更新时间
    mutex        sync.RWMutex       // 并发安全锁
}
```

**核心方法**：
- `AddPlayer(player *Player)` - 添加玩家到房间
- `StartGame()` - 开始游戏
- `ProcessPayday()` - 处理发薪日逻辑
- `NextPhase()` - 进入下一游戏阶段

### Message (通信协议)
```go
type Message struct {
    Type     MessageType // 消息类型枚举
    Data     interface{} // 消息数据载荷
    PlayerID string      // 发送方玩家ID
    GameID   string      // 目标游戏房间ID
}
```

**消息类型**：
- `connect` - 连接确认
- `join_game` - 加入游戏请求
- `start_game` - 开始游戏请求
- `game_state` - 游戏状态更新
- `payday` - 发薪日事件
- `buy_asset` - 购买资产请求
- `error` - 错误消息
- `info` - 信息通知

## 🚀 部署运行

### 环境要求
- **Go**: 1.25.0+
- **Node.js**: 18+
- **pnpm**: 8+

### 启动命令
```bash
# 后端启动
cd server && go run main.go
# 服务地址: http://localhost:9070

# 前端启动
cd webgame && pnpm install && pnpm run dev
# 服务地址: http://localhost:5177
```

### 端口配置
- **后端HTTP服务**: 8080
- **WebSocket服务**: ws://localhost:9070/ws
- **前端开发服务**: 5177

## 🛠️ 开发指南

### 添加新功能流程
1. **后端扩展**：在model/定义数据结构，main.go添加处理逻辑
2. **前端扩展**：GameScene.ts添加UI和交互逻辑
3. **通信协议**：message.go定义新消息类型
4. **测试验证**：确保前后端功能正常

### 代码规范
- **Go代码**：遵循gofmt格式化，添加详细中文注释
- **TypeScript**：严格类型检查，ESLint代码规范
- **Git提交**：使用中文commit message，描述具体变更

### 调试技巧
- **后端调试**：查看控制台日志输出
- **前端调试**：浏览器开发者工具Console面板
- **WebSocket调试**：Network面板监控WS连接状态

## 📈 项目状态

### 当前版本: v0.1.0 (MVP)
**已完成功能**：
- ✅ 基础架构搭建 - Clean Architecture + WebSocket
- ✅ 核心游戏循环 - 发薪日自动化处理
- ✅ 财务系统计算 - 现金流实时更新
- ✅ 多人房间管理 - 并发安全的状态同步
- ✅ 基础UI界面 - Phaser游戏场景

**技术指标**：
- **并发性能**：支持多房间并发，内存安全
- **响应延迟**：WebSocket < 50ms响应时间
- **代码质量**：TypeScript类型安全，Go简洁架构
- **用户体验**：60FPS流畅运行，响应式设计

### 下一步计划
1. **完善资产购买功能** - 实现完整的投资交易流程
2. **添加市场机会卡片** - 引入随机事件和投资机会
3. **实现胜利条件判断** - 财务自由目标检测
4. **优化UI/UX设计** - 提升视觉效果和交互体验

---

**项目路径**: `/Users/<USER>/code/game/cashfree`  
**文档更新**: 2025年9月2日  
**技术栈**: Go + TypeScript + Phaser + WebSocket
