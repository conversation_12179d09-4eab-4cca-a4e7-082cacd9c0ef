# 现金流游戏 (CashFlow Game)

一个基于《富爸爸穷爸爸》现金流游戏的在线多人版本，采用前后端分离架构，支持实时多人对战。

## 📋 目录

- [产品功能](#产品功能)
- [技术架构](#技术架构)
- [关键设计](#关键设计)
- [主要流程](#主要流程)
- [数据结构](#数据结构)
- [快速开始](#快速开始)
- [开发指南](#开发指南)

## 🎮 产品功能

### 核心游戏机制
- **现金流管理**：模拟真实的收入、支出和现金流计算
- **资产投资**：购买股票、房地产、企业等投资资产
- **被动收入**：通过资产获得持续的被动收入
- **游戏循环**：发薪日 → 市场机会 → 小支出 → 下个月

### 多人游戏功能
- **实时房间系统**：支持最多4人同时游戏
- **状态同步**：所有玩家实时看到相同的游戏状态
- **WebSocket通信**：低延迟的实时交互

### 用户界面
- **财务报表**：实时显示现金、现金流、收入、支出
- **资产管理**：查看和管理已购买的投资资产
- **游戏状态**：显示当前游戏阶段和月份
- **操作面板**：加入游戏、开始游戏等交互按钮

### 扩展功能（规划中）
- **角色系统**：不同职业的起始条件和成长路径
- **事件卡片**：市场机会、人生事件、意外支出
- **动态经济**：通胀、利率变化、黑天鹅事件
- **AI玩家**：单机模式下的电脑对手

## 🏗️ 技术架构

### 整体架构
```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   前端 (Web)    │ ←──────────────→ │   后端 (Go)     │
│                 │                 │                 │
│ • Phaser.js     │                 │ • HTTP Server   │
│ • TypeScript    │                 │ • WebSocket     │
│ • Vite          │                 │ • Game Logic    │
│ • pnpm          │                 │ • Clean Arch    │
└─────────────────┘                 └─────────────────┘
```

### 前端技术栈
- **游戏引擎**：Phaser.js 3.90.0 - 2D游戏框架
- **开发语言**：TypeScript - 类型安全的JavaScript
- **构建工具**：Vite 7.1.4 - 快速的前端构建工具
- **包管理器**：pnpm - 高效的包管理器
- **通信协议**：WebSocket - 实时双向通信

### 后端技术栈
- **开发语言**：Go 1.25.0 - 高性能系统语言
- **Web框架**：net/http - Go标准库HTTP服务器
- **WebSocket**：gorilla/websocket - WebSocket连接管理
- **架构模式**：Clean Architecture - 简洁架构原则

### 项目结构
```
cashfree/
├── server/                 # 后端Go项目
│   ├── model/             # 数据模型层
│   │   ├── player.go      # 玩家模型
│   │   ├── game.go        # 游戏状态模型
│   │   └── message.go     # 消息通信模型
│   ├── main.go            # 服务器入口
│   └── go.mod             # Go模块配置
├── webgame/               # 前端游戏项目
│   ├── src/
│   │   ├── scenes/        # 游戏场景
│   │   │   └── GameScene.ts
│   │   └── main.ts        # 游戏入口
│   ├── vite.config.ts     # Vite配置
│   ├── tsconfig.json      # TypeScript配置
│   └── package.json       # 前端依赖
└── README.md              # 项目文档
```

## 🎯 关键设计

### 1. 简洁架构 (Clean Architecture)
后端采用分层架构设计：
- **模型层 (Model)**：纯业务逻辑，不依赖外部框架
- **服务层 (Service)**：业务用例实现
- **接口层 (Handler)**：HTTP和WebSocket处理
- **基础设施层 (Infrastructure)**：数据存储、外部服务

### 2. 实时状态同步
- **事件驱动**：基于WebSocket的事件驱动架构
- **状态广播**：游戏状态变化自动广播给所有玩家
- **消息类型化**：定义明确的消息类型和数据结构

### 3. 并发安全设计
- **读写锁**：使用sync.RWMutex保护共享数据
- **原子操作**：关键数据更新使用原子操作
- **连接管理**：安全的WebSocket连接生命周期管理

### 4. 可扩展性设计
- **模块化结构**：清晰的模块边界，便于功能扩展
- **插件化事件**：事件系统支持动态添加新类型
- **配置驱动**：游戏规则通过配置文件调整

## 🔄 主要流程

### 1. 游戏启动流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器
    
    C->>S: WebSocket连接
    S->>C: 连接确认
    C->>S: 加入游戏请求
    S->>S: 创建/获取游戏房间
    S->>S: 创建玩家实例
    S->>C: 玩家加入成功
    S->>C: 广播游戏状态
```

### 2. 游戏循环流程
```mermaid
stateDiagram-v2
    [*] --> 等待中
    等待中 --> 发薪日: 开始游戏
    发薪日 --> 市场机会: 发薪完成
    市场机会 --> 小支出: 机会处理完成
    小支出 --> 发薪日: 进入下个月
    发薪日 --> 游戏结束: 达成胜利条件
    游戏结束 --> [*]
```

### 3. 现金流计算流程
1. **收入计算**：工资 + 被动收入
2. **支出计算**：固定支出 + 变动支出
3. **现金流更新**：被动收入 - 总支出
4. **现金更新**：现金 + 现金流
5. **状态同步**：广播更新给所有玩家

### 4. 资产购买流程
1. **资金检查**：验证玩家现金是否充足
2. **资产添加**：将资产加入玩家资产列表
3. **现金扣除**：扣除相应的购买金额
4. **收入更新**：如果资产产生被动收入，添加到收入列表
5. **现金流重算**：重新计算玩家月现金流

## 📊 数据结构

### 核心数据模型

#### Account (账号模型) - 持久化
```go
type Account struct {
    ID            string         `json:"id"`
    Username      string         `json:"username"`      // 登录用户名
    Nickname      string         `json:"nickname"`      // 显示昵称
    Avatar        string         `json:"avatar"`        // 头像URL
    Status        AccountStatus  `json:"status"`        // 当前状态
    CurrentRoomID string         `json:"current_room_id,omitempty"` // 当前房间ID
    Statistics    GameStatistics `json:"statistics"`    // 游戏统计数据
    LastLoginAt   time.Time      `json:"last_login_at"` // 最后登录时间
    CreatedAt     time.Time      `json:"created_at"`
    UpdatedAt     time.Time      `json:"updated_at"`
}
```

#### Player (游戏内玩家) - 临时
```go
type Player struct {
    GameID            string    `json:"game_id"`            // 所属游戏局ID
    AccountID         string    `json:"account_id"`         // 关联的账号ID
    Profession        string    `json:"profession"`         // 职业信息
    Cash              int       `json:"cash"`               // 现金余额
    CashFlow          int       `json:"cash_flow"`          // 每月现金流
    Assets            []Asset   `json:"assets"`             // 资产列表
    Incomes           []Income  `json:"incomes"`            // 收入列表
    Expenses          []Expense `json:"expenses"`           // 支出列表
    GameTurn          int       `json:"game_turn"`          // 当前回合数
    IsFinanciallyFree bool      `json:"is_financially_free"` // 是否达成财务自由
    CreatedAt         time.Time `json:"created_at"`         // 游戏开始时间
    UpdatedAt         time.Time `json:"updated_at"`
}
```

#### GameState (游戏状态)
```go
type GameState struct {
    ID           string             `json:"id"`            // 房间ID
    Players      map[string]*Player `json:"players"`       // 玩家列表
    CurrentMonth int                `json:"current_month"` // 当前月份
    Phase        GamePhase          `json:"phase"`         // 游戏阶段
    IsStarted    bool               `json:"is_started"`    // 是否已开始
    MaxPlayers   int                `json:"max_players"`   // 最大玩家数
    CreatedAt    time.Time          `json:"created_at"`
    UpdatedAt    time.Time          `json:"updated_at"`
}
```

#### Asset (资产)
```go
type Asset struct {
    ID            string `json:"id"`             // 资产ID
    Type          string `json:"type"`           // 资产类型
    Name          string `json:"name"`           // 资产名称
    PurchasePrice int    `json:"purchase_price"` // 购买价格
    CurrentValue  int    `json:"current_value"`  // 当前价值
    MonthlyIncome int    `json:"monthly_income"` // 月收入
    Quantity      int    `json:"quantity"`       // 数量
}
```

### 消息通信协议 (v2.0 - 2025.9.2 重构)

#### Message (消息)
```go
type Message struct {
    Type     MessageType `json:"type"`               // 消息类型
    Data     interface{} `json:"data"`               // 消息数据
    PlayerID string      `json:"player_id,omitempty"` // 玩家ID
    GameID   string      `json:"game_id,omitempty"`   // 游戏ID
}
```

#### 消息分类系统 (6大类)

**1. 连接状态类 (`connection.*`)**
- `connection.connect` - 客户端连接成功
- `connection.disconnect` - 客户端断开连接
- `connection.heartbeat` - 心跳检测
- `connection.reconnect` - 重连成功

**2. 房间管理类 (`room.*`)**
- `room.join` - 加入房间
- `room.leave` - 离开房间
- `room.state` - 房间状态更新
- `room.list` - 房间列表
- `room.create` - 创建房间
- `room.destroy` - 销毁房间

**3. 游戏管理类 (`game.*`)**
- `game.start` - 开始游戏
- `game.pause` - 暂停游戏
- `game.resume` - 恢复游戏
- `game.end` - 结束游戏
- `game.exit` - 退出游戏
- `game.state` - 游戏状态同步

**4. 游戏内玩家操作类 (`player.*`)**
- `player.roll_dice` - 投掷骰子
- `player.make_choice` - 做出选择
- `player.buy_asset` - 购买资产
- `player.sell_asset` - 出售资产
- `player.take_loan` - 借贷
- `player.repay_loan` - 还贷
- `player.end_turn` - 结束回合
- `player.chat` - 聊天消息

**5. 服务器响应类 (`server.*`)**
- `server.dice_result` - 骰子结果
- `server.move_result` - 移动结果
- `server.event_triggered` - 触发事件
- `server.choice_required` - 需要玩家选择
- `server.action_result` - 操作结果
- `server.player_update` - 玩家状态更新
- `server.turn_changed` - 回合切换
- `server.phase_changed` - 阶段切换
- `server.payday_processed` - 发薪日处理完成

**6. 游戏内系统事件类 (`system.*`)**
- `system.event` - 通用系统事件
- `system.black_swan` - 黑天鹅事件
- `system.market_crash` - 市场崩盘
- `system.economic_boom` - 经济繁荣
- `system.inflation` - 通胀变化
- `system.interest_rate` - 利率变化
- `system.news` - 新闻事件
- `system.seasonal` - 季节性事件

**通用消息类型**
- `error` - 错误消息
- `success` - 成功消息
- `info` - 信息通知
- `warning` - 警告消息

## 🚀 快速开始

### 环境要求
- Go 1.25.0+
- Node.js 18+
- pnpm 8+

### 启动后端
```bash
cd server
go run main.go
# 服务器启动在 http://localhost:9070
```

### 启动前端
```bash
cd webgame
pnpm install
pnpm run dev
# 前端启动在 http://localhost:5177
```

### 游戏体验
1. 访问前端地址
2. 点击"加入游戏"
3. 点击"开始游戏"
4. 观察现金流变化

## 🛠️ 开发指南

### 添加新功能
1. **后端**：在`model/`中定义数据结构，在`main.go`中添加处理逻辑
2. **前端**：在`GameScene.ts`中添加UI和交互逻辑
3. **通信**：在`message.go`中定义新的消息类型

### 扩展游戏规则
1. 修改`Player`模型添加新属性
2. 在`GameState`中添加新的游戏阶段
3. 实现相应的业务逻辑方法

### 调试技巧
- 后端日志：查看控制台输出
- 前端调试：使用浏览器开发者工具
- WebSocket：监控Network面板的WS连接

## 📈 未来规划

### 短期目标
- [ ] 完善资产购买功能
- [ ] 添加市场机会卡片
- [ ] 实现胜利条件判断
- [ ] 优化UI/UX设计

### 中期目标
- [ ] 多角色职业系统
- [ ] 人生事件卡片
- [ ] AI玩家对手
- [ ] 游戏回放功能

### 长期目标
- [ ] 移动端适配
- [ ] 排行榜系统
- [ ] 社交功能
- [ ] 自定义游戏规则

## 🎯 项目状态

### 当前版本：v0.2.0 (消息系统重构版)
- ✅ 基础架构搭建完成
- ✅ WebSocket实时通信
- ✅ 核心游戏循环
- ✅ 财务系统计算
- ✅ 多人房间管理
- ✅ 基础UI界面
- ✅ **消息分类系统重构** (2025.9.2)
  - 6大类消息分类体系
  - 命名空间化消息类型
  - 完整的数据结构定义
  - 前后端消息处理统一
  - 投掷骰子功能实现
  - 聊天系统基础框架
  - 心跳检测机制

### 技术指标
- **后端性能**：支持并发连接，内存安全
- **前端体验**：60FPS流畅运行，响应式设计
- **通信延迟**：< 50ms WebSocket响应
- **代码质量**：TypeScript类型安全，Go简洁架构

---

**开发团队**：基于Clean Architecture和现代Web技术构建  
**项目地址**：/Users/<USER>/code/game/cashfree  
**最后更新**：2025年9月2日 - 消息系统重构完成

## 📝 更新日志

### v0.2.0 (2025.9.2) - 消息系统重构
**重大变更：消息分类系统重构**
- 🔄 **消息类型重新设计**：从简单枚举升级为6大类分层体系
- 🏗️ **命名空间化**：使用点分隔符区分消息类别 (如 `room.join`, `player.roll_dice`)
- 📊 **数据结构完善**：为每种消息类型定义专门的数据结构
- 🔗 **前后端统一**：前后端使用相同的消息类型定义
- ⚡ **新增功能**：
  - 投掷骰子功能 (`player.roll_dice` → `server.dice_result`)
  - 聊天系统框架 (`player.chat`)
  - 心跳检测机制 (`connection.heartbeat`)
  - 房间管理优化 (`room.*` 系列消息)
- 🛠️ **技术改进**：
  - 更好的错误处理和消息验证
  - 扩展性更强的消息处理架构
  - 完整的消息处理函数框架

**影响范围：**
- `server/model/message.go` - 消息定义重构
- `server/main.go` - 消息处理逻辑重构  
- `webgame/src/scenes/GameScene.ts` - 前端消息处理重构

### v0.1.0 (2025.9.1) - MVP版本
- ✅ 基础架构搭建
- ✅ WebSocket实时通信
- ✅ 核心游戏循环
- ✅ 财务系统计算
- ✅ 多人房间管理
- ✅ 基础UI界面
游戏可以联机，前后端分离架构，通过 websocket 或其他方式通信
./server 是后端代码，使用Go，遵循简洁架构
./webgame 是前端代码，可以在网页里游玩，使用 phaser 框架 和 typescript 语言，并使用 pnpm 和 vite 管理项目
未来也许还会有其他的文件夹来表示其他的客户端

{{ ... }}
1. 支持选择不同的角色，每个角色拥有独特的起始条件、技能、限制和成长路径，影响玩家的策略选择。
2. 引入“人生事件卡”或“剧情节点”，玩家在关键节点做出选择，影响后续发展。
3. 动态经济系统（增强真实感）通货膨胀、利率变化、黑天鹅事件、生病、行业寒冬

当然不是每个玩法都强制打开，可以后续以扩展包的方式进行，开局时可以选择开关

游戏以局为单位进行，每局都有多个人共同进行，前期可以是单机游戏，一个玩家和多个AI进行，后期要引入多人联机游戏。
