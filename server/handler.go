package main

import (
	"cash/model"
	"context"
	"fmt"
	"log"

	"github.com/gorilla/websocket"
)

type Handler struct{}

var handler = &Handler{}

type RoomJoinMsg struct {
	RoomID    string `json:"room_id"`
	AccountID string `json:"account_id"`
}

func (*Handler) RoomJoin(ctx context.Context, msg *model.Message, conn *websocket.Conn) error {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		sendError(conn, "invalid_data", "Invalid join room data")
		return fmt.Errorf("invalid join room data")
	}

	// 解析加入房间数据
	userID, _ := data["user_id"].(string)
	userName, _ := data["user_name"].(string)
	roomID, _ := data["room_id"].(string)

	// 兼容性检查：优先使用完整的Account对象
	var account *model.Account
	if accountData, hasAccount := data["account"].(map[string]interface{}); hasAccount {
		// 创建Account对象
		account = &model.Account{
			ID:       userID,
			Nickname: userName,
			Status:   model.StatusOnline,
		}

		// 解析基本信息
		if id, ok := accountData["id"].(string); ok {
			account.ID = id
			userID = id
		}
		if nickname, ok := accountData["nickname"].(string); ok {
			account.Nickname = nickname
			userName = nickname
		}
		if username, ok := accountData["username"].(string); ok {
			account.Username = username
		}
		if avatar, ok := accountData["avatar"].(string); ok {
			account.Avatar = avatar
		}

		// 解析统计数据
		if statsData, ok := accountData["statistics"].(map[string]interface{}); ok {
			if totalGames, ok := statsData["total_games"].(float64); ok {
				account.Statistics.TotalGames = int(totalGames)
			}
			if winGames, ok := statsData["win_games"].(float64); ok {
				account.Statistics.WinGames = int(winGames)
			}
			if bestCashFlow, ok := statsData["best_cash_flow"].(float64); ok {
				account.Statistics.BestCashFlow = int(bestCashFlow)
			}
		}
	} else {
		// 如果没有完整的Account对象，创建一个基本的
		account = &model.Account{
			ID:       userID,
			Nickname: userName,
			Status:   model.StatusOnline,
		}
	}

	if userID == "" || userName == "" || roomID == "" {
		sendError(conn, "missing_data", "Account ID, nickname and room ID are required")
		return fmt.Errorf("missing required fields: user_id, user_name, room_id")
	}

	// 检查房间是否存在
	gameManager.mutex.RLock()
	room, exists := gameManager.rooms[roomID]
	gameManager.mutex.RUnlock()

	if !exists {
		sendError(conn, "room_not_found", "Room not found")
		return fmt.Errorf("room not found: %s", roomID)
	}

	// 添加账号到房间
	if room.AddAccount(account) {
		// 记录用户游戏房间
		gameManager.mutex.Lock()
		gameManager.userGameRooms[userID] = roomID
		gameManager.mutex.Unlock()

		// 发送成功消息给加入的玩家
		response := model.Message{
			Type: model.MsgJoinRoom,
			Data: model.SuccessData{
				Message: "Successfully joined room",
				Data: map[string]interface{}{
					"account": account,
					"room_id": roomID,
				},
			},
			PlayerID: userID,
			GameID:   roomID,
		}
		sendMessage(conn, response)

		// 广播玩家加入消息给房间内其他玩家
		joinNotification := model.Message{
			Type: "room.join",
			Data: map[string]interface{}{
				"player_id":   userID,
				"player_name": userName,
				"room_id":     roomID,
				"message":     fmt.Sprintf("%s 加入了房间", userName),
			},
			GameID: roomID,
		}
		broadcastToRoom(roomID, joinNotification, userID) // 排除刚加入的玩家

		// 广播房间状态更新
		broadcastRoomState(roomID)

		log.Printf("User %s (%s) successfully joined room %s", userName, userID, roomID)
		return nil
	} else {
		sendError(conn, "join_failed", "Failed to join room (room full or game started)")
		return fmt.Errorf("failed to join room: room full or game started")
	}
}
