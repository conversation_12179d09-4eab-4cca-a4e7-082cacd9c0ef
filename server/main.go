package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"cash/model"

	"github.com/gorilla/websocket"
)

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}

// main 是程序的入口函数。
//
// 调用场景:
//
//	应用程序启动时由 Go 运行时调用。
//
// 主要逻辑:
//  1. 设置路由，将 "/health" 路径映射到 healthCheckHandler。
//  2. 启动一个 HTTP 服务器，监听在 8080 端口。
//  3. 如果服务器启动失败，记录致命错误并退出程序。
func main() {
	// 注册健康检查的 handler
	http.HandleFunc("/health", healthCheckHandler)
	// 注册 WebSocket 的 handler
	http.HandleFunc("/ws", wsHandler)

	// 定义服务器监听的端口
	port := ":9070"
	log.Printf("Server is starting on port %s", port)
	log.Fatal(http.ListenAndServe(port, nil))
}

// healthCheckHandler 是一个 HTTP 处理器，用于响应健康检查请求。
//
// 调用场景:
//
//	当有 HTTP 请求访问 "/health" 路径时，由 net/http 库调用。
//
// 主要逻辑:
//  1. 设置响应头的 Content-Type 为 "text/plain"。
//  2. 向客户端写入 "OK" 字符串作为响应体。
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/plain")
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write([]byte("OK"))
}

// GameManager 游戏管理器，负责管理所有游戏房间和玩家连接
type GameManager struct {
	rooms         map[string]*model.RoomState // 房间列表
	connections   map[string]*websocket.Conn  // 连接ID到WebSocket连接的映射
	userSessions  map[string]string           // 用户ID到连接ID的映射
	userGameRooms map[string]string           // 用户ID到游戏房间ID的映射
	mutex         sync.RWMutex                // 并发安全锁
}

// 全局游戏管理器实例
var gameManager = &GameManager{
	rooms:         make(map[string]*model.RoomState),
	connections:   make(map[string]*websocket.Conn),
	userSessions:  make(map[string]string),
	userGameRooms: make(map[string]string),
}

// upgrader 用于将 HTTP 连接升级为 WebSocket 连接。
//
// 主要逻辑:
//   - CheckOrigin 允许所有来源的连接，这在开发阶段很方便，
//     但在生产环境中应该配置为只允许特定的域名。
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// wsHandler 处理 WebSocket 连接请求。
//
// 调用场景:
//
//	当有 HTTP 请求访问 "/ws" 路径时被调用。
//
// 主要逻辑:
//  1. 使用 upgrader 将 HTTP 连接升级为 WebSocket 连接。
//  2. 为每个连接分配唯一ID并注册到游戏管理器。
//  3. 启动消息处理循环，处理各种游戏消息。
//  4. 连接断开时清理资源。
func wsHandler(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	// 生成连接ID
	connectionID := fmt.Sprintf("conn_%d", time.Now().UnixNano())

	// 注册连接
	gameManager.mutex.Lock()
	gameManager.connections[connectionID] = conn
	gameManager.mutex.Unlock()

	log.Printf("Client connected: %s", connectionID)

	// 发送连接确认消息
	welcomeMsg := model.Message{
		Type: model.MsgConnect,
		Data: model.InfoData{Message: "Connected to game server"},
	}
	sendMessage(conn, welcomeMsg)

	// 消息处理循环
	for {
		var msg model.Message
		err := conn.ReadJSON(&msg)
		if err != nil {
			log.Printf("Failed to read message: %v", err)
			break
		}

		log.Println("Received message", "connectionID", connectionID, "type", msg.Type, "playerID", msg.PlayerID)

		// 处理消息
		handleMessage(connectionID, conn, msg)
	}

	// 清理连接
	handleDisconnection(connectionID)
}

// handleMessage 处理收到的 WebSocket 消息
//
// 调用场景:
//
//	当从客户端收到消息时调用
//
// 主要逻辑:
//
//	根据消息类型执行相应的游戏逻辑
func handleMessage(connectionID string, conn *websocket.Conn, msg model.Message) {
	ctx := context.Background()
	switch msg.Type {
	// 连接管理消息
	case model.MsgConnect:
		handleConnect(connectionID, conn, msg)
	case model.MsgHeartbeat:
		handleHeartbeat(connectionID, conn, msg)

	// 房间管理消息
	case model.MsgJoinRoom:
		handler.RoomJoin(ctx, &msg)
		handleJoinRoom(connectionID, conn, msg)
	case model.MsgLeaveRoom:
		handleLeaveRoom(connectionID, conn, msg)
	case model.MsgCreateRoom:
		handleCreateRoom(connectionID, conn, msg)
	case model.MsgRoomList:
		handleRoomList(connectionID, conn, msg)
	case model.MsgRoomState:
		handleRoomState(connectionID, conn, msg)

	// 游戏管理消息
	case model.MsgGameStart:
		handleGameStart(connectionID, conn, msg)
	case model.MsgGamePause:
		handleGamePause(connectionID, conn, msg)
	case model.MsgGameEnd:
		handleGameEnd(connectionID, conn, msg)

	// 玩家操作消息
	case model.MsgPlayerRollDice:
		handlePlayerRollDice(connectionID, conn, msg)
	case model.MsgPlayerMakeChoice:
		handlePlayerMakeChoice(connectionID, conn, msg)
	case model.MsgPlayerBuyAsset:
		handlePlayerBuyAsset(connectionID, conn, msg)
	case model.MsgPlayerSellAsset:
		handlePlayerSellAsset(connectionID, conn, msg)
	case model.MsgPlayerTakeLoan:
		handlePlayerTakeLoan(connectionID, conn, msg)
	case model.MsgPlayerRepayLoan:
		handlePlayerRepayLoan(connectionID, conn, msg)
	case model.MsgPlayerEndTurn:
		handlePlayerEndTurn(connectionID, conn, msg)
	case model.MsgPlayerChat:
		handlePlayerChat(connectionID, conn, msg)

	default:
		// 未知消息类型，返回错误
		sendError(conn, "unknown_message_type", fmt.Sprintf("Unknown message type: %s", msg.Type))
	}
}

// ===== 连接管理处理函数 =====

// handleConnect 处理用户连接请求
func handleConnect(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		sendError(conn, "invalid_data", "Invalid connect data")
		return
	}

	// 解析账号信息
	userID, _ := data["user_id"].(string)
	userName, _ := data["user_name"].(string)

	// 兼容性检查：优先使用完整的Account对象
	var account *model.Account
	if accountData, hasAccount := data["account"].(map[string]interface{}); hasAccount {
		// 解析Account对象
		account = &model.Account{}
		if id, ok := accountData["id"].(string); ok {
			account.ID = id
		}
		if nickname, ok := accountData["nickname"].(string); ok {
			account.Nickname = nickname
		}
		if username, ok := accountData["username"].(string); ok {
			account.Username = username
		}
		if avatar, ok := accountData["avatar"].(string); ok {
			account.Avatar = avatar
		}
		if status, ok := accountData["status"].(string); ok {
			account.Status = model.AccountStatus(status)
		}
		if roomID, ok := accountData["current_room_id"].(string); ok {
			account.CurrentRoomID = roomID
		}
		// 解析统计数据
		if statsData, ok := accountData["statistics"].(map[string]interface{}); ok {
			if totalGames, ok := statsData["total_games"].(float64); ok {
				account.Statistics.TotalGames = int(totalGames)
			}
			if winGames, ok := statsData["win_games"].(float64); ok {
				account.Statistics.WinGames = int(winGames)
			}
			if bestCashFlow, ok := statsData["best_cash_flow"].(float64); ok {
				account.Statistics.BestCashFlow = int(bestCashFlow)
			}
		}

		// 使用Account信息
		userID = account.ID
		userName = account.Nickname
	}

	if userID == "" || userName == "" {
		sendError(conn, "missing_user_info", "Account ID and nickname are required")
		return
	}

	gameManager.mutex.Lock()

	// 检查用户是否已经在游戏中
	if existingRoomID, exists := gameManager.userGameRooms[userID]; exists {
		// 用户正在游戏中，需要重连
		if room, roomExists := gameManager.rooms[existingRoomID]; roomExists && room.Status == model.RoomStatusInGame {
			// 更新用户会话
			if oldConnectionID, hasOldSession := gameManager.userSessions[userID]; hasOldSession {
				delete(gameManager.connections, oldConnectionID)
			}
			gameManager.userSessions[userID] = connectionID
			gameManager.mutex.Unlock()

			// 发送重连消息
			rejoinMsg := model.Message{
				Type: model.MsgGameRejoin,
				Data: map[string]interface{}{
					"room_id":    existingRoomID,
					"room_state": room,
					"message":    "Rejoining existing game",
				},
				PlayerID: userID,
				GameID:   existingRoomID,
			}
			sendMessage(conn, rejoinMsg)

			log.Printf("User %s (%s) rejoined game in room %s", userName, userID, existingRoomID)
			return
		} else {
			// 游戏已结束，清理用户游戏房间记录
			delete(gameManager.userGameRooms, userID)
		}
	}

	// 更新用户会话
	if oldConnectionID, hasOldSession := gameManager.userSessions[userID]; hasOldSession {
		delete(gameManager.connections, oldConnectionID)
	}
	gameManager.userSessions[userID] = connectionID
	gameManager.mutex.Unlock()

	// 发送连接确认消息
	response := model.Message{
		Type: model.MsgConnect,
		Data: model.InfoData{
			Message:  fmt.Sprintf("Welcome back, %s!", userName),
			Category: "connection",
		},
		PlayerID: userID,
	}
	sendMessage(conn, response)

	log.Printf("User %s (%s) connected successfully", userName, userID)
}

// handleJoinRoom 处理加入房间请求
func handleJoinRoom(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		sendError(conn, "invalid_data", "Invalid join room data")
		return
	}

	// 解析加入房间数据
	userID, _ := data["user_id"].(string)
	userName, _ := data["user_name"].(string)
	roomID, _ := data["room_id"].(string)

	// 兼容性检查：优先使用完整的Account对象
	var account *model.Account
	if accountData, hasAccount := data["account"].(map[string]interface{}); hasAccount {
		// 解析Account对象
		account = &model.Account{}
		if id, ok := accountData["id"].(string); ok {
			account.ID = id
			userID = id
		}
		if nickname, ok := accountData["nickname"].(string); ok {
			account.Nickname = nickname
			userName = nickname
		}
		if username, ok := accountData["username"].(string); ok {
			account.Username = username
		}
		if avatar, ok := accountData["avatar"].(string); ok {
			account.Avatar = avatar
		}
		if status, ok := accountData["status"].(string); ok {
			account.Status = model.AccountStatus(status)
		}
		// 解析统计数据
		if statsData, ok := accountData["statistics"].(map[string]interface{}); ok {
			if totalGames, ok := statsData["total_games"].(float64); ok {
				account.Statistics.TotalGames = int(totalGames)
			}
			if winGames, ok := statsData["win_games"].(float64); ok {
				account.Statistics.WinGames = int(winGames)
			}
			if bestCashFlow, ok := statsData["best_cash_flow"].(float64); ok {
				account.Statistics.BestCashFlow = int(bestCashFlow)
			}
		}
	} else {
		// 如果没有完整的Account对象，创建一个基本的
		account = &model.Account{
			ID:       userID,
			Nickname: userName,
			Status:   model.StatusOnline,
		}
	}

	if userID == "" || userName == "" || roomID == "" {
		sendError(conn, "missing_data", "Account ID, nickname and room ID are required")
		return
	}

	// 获取房间
	gameManager.mutex.Lock()
	room, exists := gameManager.rooms[roomID]
	if !exists {
		gameManager.mutex.Unlock()
		sendError(conn, "room_not_found", "Room not found")
		return
	}
	gameManager.mutex.Unlock()

	// 添加账号到房间
	if room.AddAccount(account) {
		// 记录用户游戏房间
		gameManager.mutex.Lock()
		gameManager.userGameRooms[userID] = roomID
		gameManager.mutex.Unlock()

		// 发送成功消息给加入的玩家
		response := model.Message{
			Type: model.MsgJoinRoom,
			Data: model.SuccessData{
				Message: "Successfully joined room",
				Data: map[string]interface{}{
					"account": account,
					"room_id": roomID,
				},
			},
			PlayerID: userID,
			GameID:   roomID,
		}
		sendMessage(conn, response)

		// 广播玩家加入消息给房间内其他玩家
		joinNotification := model.Message{
			Type: "room.join",
			Data: map[string]interface{}{
				"player_id":   userID,
				"player_name": userName,
				"room_id":     roomID,
				"message":     fmt.Sprintf("%s 加入了房间", userName),
			},
			GameID: roomID,
		}
		broadcastToRoom(roomID, joinNotification, userID) // 排除刚加入的玩家

		// 广播房间状态更新
		broadcastRoomState(roomID)
	} else {
		sendError(conn, "join_failed", "Failed to join room (room full or game started)")
	}
}

// handleGameStart 处理开始游戏请求
func handleGameStart(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	// 获取房间ID
	roomID, exists := data["room_id"].(string)
	if !exists {
		roomID = msg.GameID
	}
	if roomID == "" {
		sendError(conn, "missing_room_id", "Room ID is required")
		return
	}

	gameManager.mutex.RLock()
	room, exists := gameManager.rooms[roomID]
	gameManager.mutex.RUnlock()

	if !exists {
		sendError(conn, "room_not_found", "Room not found")
		return
	}

	// 检查是否为房主
	userID := msg.PlayerID
	if !room.IsOwner(userID) {
		sendError(conn, "not_owner", "Only room owner can start the game")
		return
	}

	if room.StartGame() {
		// 广播游戏开始消息
		startMsg := model.Message{
			Type: model.MsgGameStart,
			Data: map[string]interface{}{
				"room_id": roomID,
				"message": "Game started",
			},
			GameID: roomID,
		}
		broadcastToRoom(roomID, startMsg, "")

		// 广播房间状态更新
		broadcastRoomState(roomID)

		// 开始第一个发薪日
		go func() {
			time.Sleep(2 * time.Second) // 延迟2秒开始
			processPayday(roomID)
		}()
	} else {
		sendError(conn, "start_failed", "Failed to start game (not enough players or game already started)")
	}
}

// ===== 房间管理处理函数 =====

// handleLeaveRoom 处理离开房间请求
//
// 调用场景:
//
//	用户主动点击离开房间按钮时调用
//
// 主要逻辑:
//  1. 从房间中移除玩家
//  2. 如果房间为空则解散房间
//  3. 广播房间状态更新
func handleLeaveRoom(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		sendError(conn, "invalid_data", "Invalid leave room data")
		return
	}

	// 解析用户信息
	userID, _ := data["user_id"].(string)
	roomID, _ := data["room_id"].(string)

	if userID == "" || roomID == "" {
		sendError(conn, "missing_data", "User ID and room ID are required")
		return
	}

	// 执行退出房间逻辑
	if leaveRoom(userID, roomID, "user_request") {
		// 发送成功消息给离开的玩家
		response := model.Message{
			Type: model.MsgLeaveRoom,
			Data: model.SuccessData{
				Message: "Successfully left room",
				Data: map[string]interface{}{
					"room_id": roomID,
				},
			},
			PlayerID: userID,
		}
		sendMessage(conn, response)

		// 广播玩家离开消息给房间内其他玩家
		leaveNotification := model.Message{
			Type: "room.leave",
			Data: map[string]interface{}{
				"player_id": userID,
				"room_id":   roomID,
				"message":   fmt.Sprintf("玩家 %s 离开了房间", userID),
			},
			GameID: roomID,
		}
		broadcastToRoom(roomID, leaveNotification, userID) // 排除已离开的玩家
	} else {
		sendError(conn, "leave_failed", "Failed to leave room")
	}
}

// handleRoomList 处理房间列表查询请求
func handleRoomList(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	// 检查是否只查询可用房间
	onlyAvailable := false
	if oa, exists := data["only_available"]; exists {
		if oaBool, ok := oa.(bool); ok {
			onlyAvailable = oaBool
		}
	}

	gameManager.mutex.RLock()
	var availableRooms []map[string]interface{}

	for roomID, room := range gameManager.rooms {
		// 如果只查询可用房间，跳过已满或已开始的房间
		if onlyAvailable {
			if room.Status == model.RoomStatusInGame || room.GetAccountCount() >= room.MaxPlayers {
				continue
			}
		}

		roomInfo := map[string]interface{}{
			"room_id":      roomID,
			"room_name":    room.RoomName,
			"player_count": room.GetAccountCount(),
			"max_players":  room.MaxPlayers,
			"is_started":   room.Status == model.RoomStatusInGame,
			"room_owner":   room.RoomOwner,
			"status":       string(room.Status),
		}
		availableRooms = append(availableRooms, roomInfo)
	}
	gameManager.mutex.RUnlock()

	// 发送房间列表响应
	response := model.Message{
		Type: model.MsgRoomList,
		Data: map[string]interface{}{
			"rooms": availableRooms,
			"total": len(availableRooms),
		},
		PlayerID: connectionID,
	}
	sendMessage(conn, response)
}

// handleRoomState 处理房间状态查询请求
func handleRoomState(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		sendError(conn, "invalid_data", "Invalid room state request data")
		return
	}

	// 获取房间ID
	roomID, exists := data["room_id"].(string)
	if !exists {
		roomID = "default_game" // 默认房间
	}

	gameManager.mutex.RLock()
	room, exists := gameManager.rooms[roomID]
	gameManager.mutex.RUnlock()

	if !exists {
		sendError(conn, "room_not_found", "Room not found")
		return
	}

	// 发送房间状态
	response := model.Message{
		Type:     model.MsgRoomState,
		Data:     room,
		GameID:   roomID,
		PlayerID: connectionID,
	}
	sendMessage(conn, response)
}

// handleCreateRoom 处理创建房间请求
func handleCreateRoom(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		sendError(conn, "invalid_data", "Invalid request data")
		return
	}

	// 获取房间配置
	roomName, _ := data["room_name"].(string)
	if roomName == "" {
		roomName = "Room " + generateRoomID()
	}

	maxPlayers := 4 // 默认最大玩家数
	if mp, exists := data["max_players"]; exists {
		if mpFloat, ok := mp.(float64); ok {
			maxPlayers = int(mpFloat)
		}
	}

	// 生成房间ID
	roomID := generateRoomID()

	// 创建新房间
	room := model.NewRoomState(roomID, roomName, msg.PlayerID, maxPlayers)

	gameManager.mutex.Lock()
	gameManager.rooms[roomID] = room
	gameManager.mutex.Unlock()

	// 发送创建成功响应
	response := model.Message{
		Type: model.MsgRoomCreated,
		Data: map[string]interface{}{
			"room_id":     roomID,
			"room_name":   roomName,
			"max_players": maxPlayers,
			"room_owner":  msg.PlayerID,
			"status":      string(room.Status),
		},
		PlayerID: connectionID,
		GameID:   roomID,
	}
	sendMessage(conn, response)
}

// ===== 游戏管理处理函数 =====

// handleGamePause 处理暂停游戏请求
func handleGamePause(connectionID string, conn *websocket.Conn, msg model.Message) {
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "Game pause not implemented yet"},
	})
}

// handleGameEnd 处理结束游戏请求
//
// 调用场景:
//
//	游戏正常结束或管理员强制结束游戏时调用
//
// 主要逻辑:
//  1. 结束游戏状态
//  2. 清理已断开连接的用户
//  3. 广播游戏结束消息
func handleGameEnd(connectionID string, conn *websocket.Conn, msg model.Message) {
	data, ok := msg.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	// 获取房间ID
	roomID, exists := data["room_id"].(string)
	if !exists {
		roomID = msg.GameID
	}
	if roomID == "" {
		sendError(conn, "missing_room_id", "Room ID is required")
		return
	}

	gameManager.mutex.Lock()
	room, exists := gameManager.rooms[roomID]
	if !exists {
		gameManager.mutex.Unlock()
		sendError(conn, "room_not_found", "Room not found")
		return
	}

	// 结束游戏
	room.EndGame()

	// 清理已断开连接的用户
	var disconnectedUsers []string
	for userID := range room.Accounts {
		if _, hasSession := gameManager.userSessions[userID]; !hasSession {
			disconnectedUsers = append(disconnectedUsers, userID)
		}
	}
	gameManager.mutex.Unlock()

	// 移除断开连接的用户
	for _, userID := range disconnectedUsers {
		leaveRoom(userID, roomID, "game_ended")
		log.Printf("Removed disconnected user %s from room %s after game ended", userID, roomID)
	}

	// 广播游戏结束消息
	broadcastRoomState(roomID)

	// 发送成功响应
	response := model.Message{
		Type: model.MsgGameEnd,
		Data: model.SuccessData{
			Message: "Game ended successfully",
			Data: map[string]interface{}{
				"room_id":                    roomID,
				"disconnected_users_removed": len(disconnectedUsers),
			},
		},
		GameID: roomID,
	}
	sendMessage(conn, response)
}

// ===== 玩家操作处理函数 =====

// handlePlayerRollDice 处理投掷骰子请求
func handlePlayerRollDice(connectionID string, conn *websocket.Conn, msg model.Message) {
	// 解析投掷骰子数据
	var diceData model.RollDiceData
	if dataMap, ok := msg.Data.(map[string]interface{}); ok {
		if count, exists := dataMap["dice_count"]; exists {
			if countInt, ok := count.(float64); ok {
				diceData.DiceCount = int(countInt)
			}
		}
	}

	// 默认投掷1个骰子
	if diceData.DiceCount <= 0 {
		diceData.DiceCount = 1
	}

	// 生成骰子结果
	diceValues := make([]int, diceData.DiceCount)
	totalSteps := 0
	for i := 0; i < diceData.DiceCount; i++ {
		value := 1 + (int(time.Now().UnixNano()) % 6) // 简单的随机数生成
		diceValues[i] = value
		totalSteps += value
	}

	// 发送骰子结果
	result := model.Message{
		Type: model.MsgDiceResult,
		Data: model.DiceResultData{
			DiceValues: diceValues,
			TotalSteps: totalSteps,
			PlayerID:   connectionID,
		},
		PlayerID: connectionID,
		GameID:   msg.GameID,
	}
	sendMessage(conn, result)
}

// handlePlayerMakeChoice 处理玩家选择请求
func handlePlayerMakeChoice(connectionID string, conn *websocket.Conn, msg model.Message) {
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "Player choice not implemented yet"},
	})
}

// handlePlayerBuyAsset 处理购买资产请求
func handlePlayerBuyAsset(connectionID string, conn *websocket.Conn, msg model.Message) {
	// 实现购买资产逻辑
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "Asset purchase not implemented yet"},
	})
}

// handlePlayerSellAsset 处理出售资产请求
func handlePlayerSellAsset(connectionID string, conn *websocket.Conn, msg model.Message) {
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "Asset sale not implemented yet"},
	})
}

// handlePlayerTakeLoan 处理借贷请求
func handlePlayerTakeLoan(connectionID string, conn *websocket.Conn, msg model.Message) {
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "Take loan not implemented yet"},
	})
}

// handlePlayerRepayLoan 处理还贷请求
func handlePlayerRepayLoan(connectionID string, conn *websocket.Conn, msg model.Message) {
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "Repay loan not implemented yet"},
	})
}

// handlePlayerEndTurn 处理结束回合请求
func handlePlayerEndTurn(connectionID string, conn *websocket.Conn, msg model.Message) {
	sendMessage(conn, model.Message{
		Type: model.MsgInfo,
		Data: model.InfoData{Message: "End turn not implemented yet"},
	})
}

// handlePlayerChat 处理聊天消息请求
func handlePlayerChat(connectionID string, conn *websocket.Conn, msg model.Message) {
	// 解析聊天数据
	var chatData model.ChatData
	if dataMap, ok := msg.Data.(map[string]interface{}); ok {
		if message, exists := dataMap["message"]; exists {
			if msgStr, ok := message.(string); ok {
				chatData.Message = msgStr
				chatData.Timestamp = time.Now().Format("15:04:05")
				chatData.IsPublic = true // 默认公开聊天
			}
		}
	}

	// 广播聊天消息给房间内所有玩家
	if chatData.Message != "" {
		chatMsg := model.Message{
			Type:     model.MsgPlayerChat,
			Data:     chatData,
			PlayerID: connectionID,
			GameID:   msg.GameID,
		}

		// 这里应该广播给房间内所有玩家，暂时只回复给发送者
		sendMessage(conn, chatMsg)
	}
}

// ===== 连接管理处理函数 =====

// handleHeartbeat 处理心跳检测请求
func handleHeartbeat(connectionID string, conn *websocket.Conn, msg model.Message) {
	// 回复心跳
	response := model.Message{
		Type: model.MsgHeartbeat,
		Data: model.InfoData{
			Message:  "pong",
			Category: "heartbeat",
		},
		PlayerID: connectionID,
	}
	sendMessage(conn, response)
}

// processPayday 处理发薪日逻辑
func processPayday(roomID string) {
	gameManager.mutex.RLock()
	room, exists := gameManager.rooms[roomID]
	gameManager.mutex.RUnlock()

	if !exists {
		return
	}

	room.GameState.ProcessPayday()
	broadcastRoomState(roomID)

	// 3秒后进入下一阶段
	time.Sleep(3 * time.Second)
	room.GameState.NextPhase()
	broadcastRoomState(roomID)
}

// broadcastRoomState 广播房间状态给所有玩家
//
// 调用场景:
//
//	房间状态发生变化时调用（玩家加入、离开、游戏开始等）
//
// 主要逻辑:
//  1. 获取房间的游戏状态
//  2. 通过用户会话映射找到对应的WebSocket连接
//  3. 向房间内所有在线玩家广播状态更新
func broadcastRoomState(roomID string) {
	gameManager.mutex.RLock()
	room, exists := gameManager.rooms[roomID]
	if !exists {
		gameManager.mutex.RUnlock()
		return
	}

	// 创建房间状态消息
	msg := model.Message{
		Type:   model.MsgRoomState,
		Data:   room,
		GameID: roomID,
	}

	// 向所有玩家广播 - 修复连接映射问题
	for accountID := range room.Accounts {
		// 通过用户会话映射找到连接ID
		if connectionID, hasSession := gameManager.userSessions[accountID]; hasSession {
			// 通过连接ID找到WebSocket连接
			if conn, hasConnection := gameManager.connections[connectionID]; hasConnection {
				sendMessage(conn, msg)
			}
		}
	}
	gameManager.mutex.RUnlock()
}

// broadcastToRoom 向房间内指定玩家广播消息
//
// 调用场景:
//
//	需要向房间内特定玩家发送通知消息时调用
//
// 主要逻辑:
//  1. 获取房间内的所有玩家
//  2. 排除指定的玩家ID（如发送者）
//  3. 向其他玩家广播消息
func broadcastToRoom(roomID string, msg model.Message, excludePlayerID string) {
	gameManager.mutex.RLock()
	room, exists := gameManager.rooms[roomID]
	if !exists {
		gameManager.mutex.RUnlock()
		return
	}

	// 向房间内所有玩家广播（排除指定玩家）
	for accountID := range room.Accounts {
		if accountID == excludePlayerID {
			continue // 跳过排除的玩家
		}

		// 通过用户会话映射找到连接ID
		if connectionID, hasSession := gameManager.userSessions[accountID]; hasSession {
			// 通过连接ID找到WebSocket连接
			if conn, hasConnection := gameManager.connections[connectionID]; hasConnection {
				sendMessage(conn, msg)
			}
		}
	}
	gameManager.mutex.RUnlock()
}

// sendMessage 发送消息到客户端
func sendMessage(conn *websocket.Conn, msg model.Message) {
	if err := conn.WriteJSON(msg); err != nil {
		log.Printf("Failed to send message: %v", err)
	}
}

// sendError 发送错误消息到客户端
func sendError(conn *websocket.Conn, code, message string) {
	errorMsg := model.Message{
		Type: model.MsgError,
		Data: model.ErrorData{
			Code:    code,
			Message: message,
		},
	}
	sendMessage(conn, errorMsg)
}

// ===== 连接和房间管理辅助函数 =====

// handleDisconnection 处理连接断开
//
// 调用场景:
//
//	WebSocket连接断开时调用
//
// 主要逻辑:
//  1. 清理连接映射
//  2. 查找断开的用户
//  3. 根据游戏状态决定是否触发退出房间
func handleDisconnection(connectionID string) {
	gameManager.mutex.Lock()
	delete(gameManager.connections, connectionID)

	// 查找断开连接的用户
	var disconnectedUserID string
	for userID, connID := range gameManager.userSessions {
		if connID == connectionID {
			disconnectedUserID = userID
			delete(gameManager.userSessions, userID)
			break
		}
	}
	gameManager.mutex.Unlock()

	if disconnectedUserID == "" {
		log.Printf("Client disconnected: %s", connectionID)
		return
	}

	log.Printf("User %s disconnected (connection: %s)", disconnectedUserID, connectionID)

	// 检查用户是否在房间中
	gameManager.mutex.RLock()
	roomID, inRoom := gameManager.userGameRooms[disconnectedUserID]
	gameManager.mutex.RUnlock()

	if !inRoom {
		return
	}

	// 检查房间和游戏状态
	gameManager.mutex.RLock()
	room, roomExists := gameManager.rooms[roomID]
	gameManager.mutex.RUnlock()

	if !roomExists {
		// 房间不存在，清理用户房间映射
		gameManager.mutex.Lock()
		delete(gameManager.userGameRooms, disconnectedUserID)
		gameManager.mutex.Unlock()
		return
	}

	// 根据游戏状态决定处理方式
	if room.Status != model.RoomStatusInGame {
		// 游戏未开始，触发用户退出房间
		log.Printf("Game not started, removing user %s from room %s", disconnectedUserID, roomID)
		leaveRoom(disconnectedUserID, roomID, "disconnection")
	} else {
		// 游戏已开始，保持用户在房间中，等待重连
		log.Printf("Game in progress, keeping user %s in room %s for potential reconnection", disconnectedUserID, roomID)
	}
}

// leaveRoom 执行退出房间的核心逻辑
//
// 调用场景:
//  1. 用户主动退出房间
//  2. 连接断开且游戏未开始
//  3. 游戏结束后清理断开的用户
//
// 主要逻辑:
//  1. 从房间中移除玩家
//  2. 清理用户房间映射
//  3. 检查房间是否需要解散
//  4. 广播房间状态更新
func leaveRoom(userID, roomID, reason string) bool {
	gameManager.mutex.Lock()
	defer gameManager.mutex.Unlock()

	// 检查房间是否存在
	room, exists := gameManager.rooms[roomID]
	if !exists {
		log.Printf("Room %s not found when user %s trying to leave", roomID, userID)
		return false
	}

	// 从房间中移除账号
	if !room.RemoveAccount(userID) {
		log.Printf("User %s not found in room %s", userID, roomID)
		return false
	}

	delete(gameManager.userGameRooms, userID)

	log.Printf("User %s left room %s (reason: %s), remaining accounts: %d",
		userID, roomID, reason, room.GetAccountCount())

	// 检查房间是否为空，如果为空则解散房间
	if room.GetAccountCount() == 0 {
		log.Printf("Room %s is empty, dissolving room", roomID)
		delete(gameManager.rooms, roomID)
		return true
	}

	// 如果离开的是房主，转移房主权限给第一个账号
	if room.IsOwner(userID) {
		for newOwnerID := range room.Accounts {
			room.RoomOwner = newOwnerID
			log.Printf("Room %s ownership transferred from %s to %s", roomID, userID, newOwnerID)
			break
		}
	}

	// 广播房间状态更新（在锁外执行）
	go broadcastRoomState(roomID)

	return true
}

// generateRoomID 生成唯一的房间ID
//
// 调用场景:
//
//	创建新房间时生成唯一标识符
//
// 主要逻辑:
//
//	使用当前时间戳的纳秒值确保唯一性
func generateRoomID() string {
	return fmt.Sprintf("room_%d", time.Now().UnixNano())
}
