package model

import (
	"sync"
	"time"
)

// GameState 游戏逻辑状态管理
//
// 调用场景:
//   管理具体游戏的进行状态，包括游戏进度、阶段、玩家游戏数据等
//
// 主要职责:
//   - 游戏进度管理（月份、阶段）
//   - 玩家游戏状态管理（Player对象）
//   - 游戏事件处理
//   - 游戏流程控制
type GameState struct {
	RoomID       string             `json:"room_id"`       // 关联的房间ID
	Players      map[string]*Player `json:"players"`       // 玩家ID -> 玩家游戏对象
	CurrentMonth int                `json:"current_month"` // 当前游戏月份
	Phase        GamePhase          `json:"phase"`         // 当前游戏阶段
	IsStarted    bool               `json:"is_started"`    // 游戏是否已开始
	Events       []Event            `json:"events"`        // 当前游戏事件
	StartedAt    time.Time          `json:"started_at"`    // 游戏开始时间
	UpdatedAt    time.Time          `json:"updated_at"`    // 更新时间
	mutex        sync.RWMutex       // 并发安全锁
}

// GamePhase 表示游戏的不同阶段
type GamePhase string

const (
	PhaseWaiting    GamePhase = "waiting"     // 等待玩家加入
	PhaseStarted    GamePhase = "started"     // 游戏已开始
	PhasePayday     GamePhase = "payday"      // 发薪日阶段
	PhaseMarket     GamePhase = "market"      // 市场机会阶段
	PhaseDoodad     GamePhase = "doodad"      // 小支出阶段
	PhaseFinished   GamePhase = "finished"    // 游戏结束
)

// Event 表示游戏中的事件
//
// 调用场景:
//   用于处理游戏中的各种事件，如市场机会、意外支出等
//
// 主要字段:
//   - Type: 事件类型
//   - Title: 事件标题
//   - Description: 事件描述
//   - Options: 可选择的行动
type Event struct {
	ID          string        `json:"id"`
	Type        EventType     `json:"type"`        // 事件类型
	Title       string        `json:"title"`       // 事件标题
	Description string        `json:"description"` // 事件描述
	Options     []EventOption `json:"options"`     // 可选择的行动
	TargetPlayer string       `json:"target_player,omitempty"` // 目标玩家ID（如果是个人事件）
}

// EventType 事件类型
type EventType string

const (
	EventTypeMarket    EventType = "market"     // 市场机会
	EventTypeDoodad    EventType = "doodad"     // 小支出
	EventTypeCharity   EventType = "charity"    // 慈善
	EventTypeDownsize  EventType = "downsize"   // 裁员
	EventTypeBaby      EventType = "baby"       // 生孩子
	EventTypePayRaise  EventType = "pay_raise"  // 加薪
)

// EventOption 事件选项
type EventOption struct {
	ID          string `json:"id"`
	Text        string `json:"text"`        // 选项文本
	CashChange  int    `json:"cash_change"` // 现金变化
	Description string `json:"description"` // 选项描述
}

// NewGameState 创建新的游戏状态
//
// 调用场景:
//   当房间开始游戏时调用
//
// 主要逻辑:
//   初始化游戏状态的各个字段
func NewGameState(roomID string) *GameState {
	return &GameState{
		RoomID:       roomID,
		Players:      make(map[string]*Player),
		CurrentMonth: 1,
		Phase:        PhaseWaiting,
		IsStarted:    false,
		Events:       []Event{},
		StartedAt:    time.Time{}, // 游戏开始时设置
		UpdatedAt:    time.Now(),
	}
}

// AddPlayer 添加玩家到游戏
//
// 调用场景:
//   当房间开始游戏时，将账号转换为游戏玩家
//
// 主要逻辑:
//   添加玩家到游戏状态中
func (gs *GameState) AddPlayer(player *Player) bool {
	gs.mutex.Lock()
	defer gs.mutex.Unlock()

	gs.Players[player.GetPlayerKey()] = player
	gs.UpdatedAt = time.Now()
	return true
}

// RemovePlayer 从游戏房间移除玩家
//
// 调用场景:
//   当玩家离开游戏房间时调用
func (gs *GameState) RemovePlayer(playerID string) {
	gs.mutex.Lock()
	defer gs.mutex.Unlock()

	delete(gs.Players, playerID)
	gs.UpdatedAt = time.Now()
}

// StartGame 开始游戏
//
// 调用场景:
//   当所有玩家准备就绪时调用
//
// 主要逻辑:
//   1. 检查是否有足够的玩家
//   2. 设置游戏状态为已开始
//   3. 进入第一个游戏阶段
func (gs *GameState) StartGame() bool {
	gs.mutex.Lock()
	defer gs.mutex.Unlock()

	if len(gs.Players) < 1 {
		return false // 玩家数量不足
	}

	gs.IsStarted = true
	gs.Phase = PhasePayday
	gs.UpdatedAt = time.Now()
	return true
}

// NextPhase 进入下一个游戏阶段
//
// 调用场景:
//   当当前阶段完成时调用
//
// 主要逻辑:
//   根据当前阶段决定下一个阶段
func (gs *GameState) NextPhase() {
	gs.mutex.Lock()
	defer gs.mutex.Unlock()

	switch gs.Phase {
	case PhasePayday:
		gs.Phase = PhaseMarket
	case PhaseMarket:
		gs.Phase = PhaseDoodad
	case PhaseDoodad:
		gs.Phase = PhasePayday
		gs.CurrentMonth++
	}

	gs.UpdatedAt = time.Now()
}

// ProcessPayday 处理发薪日
//
// 调用场景:
//   在发薪日阶段为所有玩家发放现金流
//
// 主要逻辑:
//   1. 为每个玩家增加现金流收入
//   2. 重新计算玩家的现金流
func (gs *GameState) ProcessPayday() {
	gs.mutex.Lock()
	defer gs.mutex.Unlock()

	for _, player := range gs.Players {
		// 发放现金流收入
		player.Cash += player.CashFlow
		player.UpdatedAt = time.Now()
	}

	gs.UpdatedAt = time.Now()
}

// GetPlayerCount 获取当前玩家数量
func (gs *GameState) GetPlayerCount() int {
	gs.mutex.RLock()
	defer gs.mutex.RUnlock()
	return len(gs.Players)
}

// GetPlayer 获取指定玩家
func (gs *GameState) GetPlayer(playerID string) (*Player, bool) {
	gs.mutex.RLock()
	defer gs.mutex.RUnlock()
	player, exists := gs.Players[playerID]
	return player, exists
}
