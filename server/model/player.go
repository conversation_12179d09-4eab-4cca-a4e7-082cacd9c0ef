package model

import "time"

// Player 表示单局游戏中的玩家财务状态
//
// 调用场景:
//   游戏开始时创建，用于管理玩家在当前游戏局中的财务状况和游戏进度
//   游戏结束后此对象生命周期结束
//
// 主要字段:
//   - GameID: 所属游戏局ID
//   - AccountID: 关联的账号ID
//   - Profession: 本局游戏选择的职业
//   - Cash: 当前现金余额
//   - CashFlow: 当前月现金流（被动收入 - 总支出）
//   - Assets: 拥有的资产列表
//   - Incomes: 收入项目列表
//   - Expenses: 支出项目列表
//   - GameTurn: 当前游戏回合数
//   - IsFinanciallyFree: 是否已达成财务自由
type Player struct {
	GameID            string    `json:"game_id"`            // 所属游戏局ID
	AccountID         string    `json:"account_id"`         // 关联的账号ID
	Profession        string    `json:"profession"`         // 职业信息
	Cash              int       `json:"cash"`               // 现金余额
	CashFlow          int       `json:"cash_flow"`          // 每月现金流
	Assets            []Asset   `json:"assets"`             // 资产列表
	Incomes           []Income  `json:"incomes"`            // 收入列表
	Expenses          []Expense `json:"expenses"`           // 支出列表
	GameTurn          int       `json:"game_turn"`          // 当前回合数
	IsFinanciallyFree bool      `json:"is_financially_free"` // 是否达成财务自由
	CreatedAt         time.Time `json:"created_at"`         // 游戏开始时间
	UpdatedAt         time.Time `json:"updated_at"`
}

// Asset 表示玩家拥有的资产
//
// 调用场景:
//   用于记录玩家购买的各种投资资产，如股票、房地产、企业等
//
// 主要字段:
//   - Type: 资产类型（股票、房地产、企业、贵金属等）
//   - Name: 资产名称
//   - PurchasePrice: 购买价格
//   - CurrentValue: 当前价值
//   - MonthlyIncome: 每月产生的被动收入
type Asset struct {
	ID            string `json:"id"`
	Type          string `json:"type"`           // 资产类型：stock, real_estate, business, precious_metal
	Name          string `json:"name"`           // 资产名称
	PurchasePrice int    `json:"purchase_price"` // 购买价格
	CurrentValue  int    `json:"current_value"`  // 当前价值
	MonthlyIncome int    `json:"monthly_income"` // 每月被动收入
	Quantity      int    `json:"quantity"`       // 数量
}

// GetPlayerKey 获取玩家在游戏中的唯一标识
//
// 调用场景:
//   在游戏状态管理中需要唯一标识玩家时调用
//
// 主要逻辑:
//   使用AccountID作为玩家的唯一标识符
func (p *Player) GetPlayerKey() string {
	return p.AccountID
}

// NextTurn 进入下一回合
//
// 调用场景:
//   玩家完成当前回合的操作后调用
//
// 主要逻辑:
//   增加回合数并更新时间戳
func (p *Player) NextTurn() {
	p.GameTurn++
	p.UpdatedAt = time.Now()
}

// CheckFinancialFreedom 检查是否达成财务自由
//
// 调用场景:
//   每回合结束时检查玩家是否达成胜利条件
//
// 主要逻辑:
//   当被动收入超过总支出时达成财务自由
func (p *Player) CheckFinancialFreedom() bool {
	if p.CashFlow > 0 && !p.IsFinanciallyFree {
		p.IsFinanciallyFree = true
		p.UpdatedAt = time.Now()
		return true
	}
	return p.IsFinanciallyFree
}

// Income 表示收入项目
//
// 调用场景:
//   用于记录玩家的各种收入来源，包括工资、被动收入等
//
// 主要字段:
//   - Type: 收入类型（工资、被动收入、一次性收入等）
//   - Source: 收入来源
//   - Amount: 收入金额
//   - IsPassive: 是否为被动收入
type Income struct {
	ID        string `json:"id"`
	Type      string `json:"type"`       // 收入类型：salary, passive, bonus, dividend
	Source    string `json:"source"`     // 收入来源
	Amount    int    `json:"amount"`     // 收入金额
	IsPassive bool   `json:"is_passive"` // 是否为被动收入
}

// Expense 表示支出项目
//
// 调用场景:
//   用于记录玩家的各种支出，包括生活费用、贷款还款等
//
// 主要字段:
//   - Type: 支出类型（生活费、贷款、税收等）
//   - Description: 支出描述
//   - Amount: 支出金额
//   - IsFixed: 是否为固定支出
type Expense struct {
	ID          string `json:"id"`
	Type        string `json:"type"`        // 支出类型：living, loan, tax, insurance
	Description string `json:"description"` // 支出描述
	Amount      int    `json:"amount"`      // 支出金额
	IsFixed     bool   `json:"is_fixed"`    // 是否为固定支出
}

// CalculateCashFlow 计算玩家的月现金流
//
// 调用场景:
//   当玩家的收入或支出发生变化时调用，重新计算现金流
//
// 主要逻辑:
//   1. 计算所有被动收入的总和
//   2. 计算所有支出的总和
//   3. 现金流 = 被动收入 - 总支出
func (p *Player) CalculateCashFlow() {
	totalPassiveIncome := 0
	totalExpenses := 0

	// 计算被动收入
	for _, income := range p.Incomes {
		if income.IsPassive {
			totalPassiveIncome += income.Amount
		}
	}

	// 计算总支出
	for _, expense := range p.Expenses {
		totalExpenses += expense.Amount
	}

	p.CashFlow = totalPassiveIncome - totalExpenses
	p.UpdatedAt = time.Now()
}

// AddAsset 添加资产到玩家的资产列表
//
// 调用场景:
//   当玩家购买新资产时调用
//
// 主要逻辑:
//   1. 检查玩家现金是否充足
//   2. 扣除现金
//   3. 添加资产到列表
//   4. 如果资产产生被动收入，添加到收入列表
func (p *Player) AddAsset(asset Asset) bool {
	totalCost := asset.PurchasePrice * asset.Quantity
	if p.Cash < totalCost {
		return false // 现金不足
	}

	p.Cash -= totalCost
	p.Assets = append(p.Assets, asset)

	// 如果资产产生被动收入，添加到收入列表
	if asset.MonthlyIncome > 0 {
		income := Income{
			ID:        "income_" + asset.ID,
			Type:      "passive",
			Source:    asset.Name,
			Amount:    asset.MonthlyIncome * asset.Quantity,
			IsPassive: true,
		}
		p.Incomes = append(p.Incomes, income)
	}

	p.CalculateCashFlow()
	return true
}
