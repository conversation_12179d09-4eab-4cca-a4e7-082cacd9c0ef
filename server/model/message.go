package model

// Message 表示 WebSocket 通信的消息结构
//
// 调用场景:
//
//	用于前后端之间的所有 WebSocket 通信
//
// 主要字段:
//   - Type: 消息类型，用于区分不同的操作
//   - Data: 消息数据，根据类型包含不同的内容
//   - PlayerID: 发送消息的玩家ID
//   - GameID: 游戏房间ID
type Message struct {
	Version  string      `json:"version"` // 消息版本，当前固定为 v2
	Type     MessageType `json:"type"`    // 消息类型，用于路由到对应的处理函数
	ReqID    string      `json:"req_id"`  // 消息序列号
	GameID   string      `json:"game_id,omitempty"`
	PlayerID string      `json:"player_id,omitempty"`
	Data     any         `json:"data"`
}

// MessageType 消息类型枚举
type MessageType string

// ===== 1. 连接状态类消息 =====
const (
	// 连接建立和断开
	MsgConnect    MessageType = "connection.connect"    // 客户端连接成功
	MsgDisconnect MessageType = "connection.disconnect" // 客户端断开连接
	MsgHeartbeat  MessageType = "connection.heartbeat"  // 心跳检测
	MsgReconnect  MessageType = "connection.reconnect"  // 重连成功
)

// ===== 2. 房间管理类消息 =====
const (
	// 房间操作
	MsgJoinRoom    MessageType = "room.join"    // 加入房间
	MsgLeaveRoom   MessageType = "room.leave"   // 离开房间
	MsgRoomState   MessageType = "room.state"   // 房间状态
	MsgRoomList    MessageType = "room.list"    // 房间列表
	MsgCreateRoom  MessageType = "room.create"  // 创建房间
	MsgRoomCreated MessageType = "room.created" // 房间创建成功
	MsgDestroyRoom MessageType = "room.destroy" // 销毁房间
)

// ===== 3. 游戏管理类消息 =====
const (
	// 游戏生命周期
	MsgGameStart  MessageType = "game.start"  // 开始游戏
	MsgGamePause  MessageType = "game.pause"  // 暂停游戏
	MsgGameResume MessageType = "game.resume" // 恢复游戏
	MsgGameEnd    MessageType = "game.end"    // 结束游戏
	MsgGameExit   MessageType = "game.exit"   // 退出游戏
	MsgGameState  MessageType = "game.state"  // 游戏状态同步
	MsgGameRejoin MessageType = "game.rejoin" // 重新加入游戏
)

// ===== 4. 游戏内玩家操作类消息 =====
const (
	// 玩家主动操作
	MsgPlayerRollDice   MessageType = "player.roll_dice"   // 投掷骰子
	MsgPlayerMakeChoice MessageType = "player.make_choice" // 做出选择
	MsgPlayerBuyAsset   MessageType = "player.buy_asset"   // 购买资产
	MsgPlayerSellAsset  MessageType = "player.sell_asset"  // 出售资产
	MsgPlayerTakeLoan   MessageType = "player.take_loan"   // 借贷
	MsgPlayerRepayLoan  MessageType = "player.repay_loan"  // 还贷
	MsgPlayerEndTurn    MessageType = "player.end_turn"    // 结束回合
	MsgPlayerChat       MessageType = "player.chat"        // 聊天消息
)

// ===== 5. 服务器响应类消息 =====
const (
	// 服务器对玩家操作的响应
	MsgDiceResult      MessageType = "server.dice_result"      // 骰子结果
	MsgMoveResult      MessageType = "server.move_result"      // 移动结果
	MsgEventTriggered  MessageType = "server.event_triggered"  // 触发事件
	MsgChoiceRequired  MessageType = "server.choice_required"  // 需要玩家选择
	MsgActionResult    MessageType = "server.action_result"    // 操作结果
	MsgPlayerUpdate    MessageType = "server.player_update"    // 玩家状态更新
	MsgTurnChanged     MessageType = "server.turn_changed"     // 回合切换
	MsgPhaseChanged    MessageType = "server.phase_changed"    // 阶段切换
	MsgPaydayProcessed MessageType = "server.payday_processed" // 发薪日处理完成
)

// ===== 6. 游戏内系统事件类消息 =====
const (
	// 系统级事件
	MsgSystemEvent     MessageType = "system.event"         // 通用系统事件
	MsgBlackSwanEvent  MessageType = "system.black_swan"    // 黑天鹅事件
	MsgMarketCrash     MessageType = "system.market_crash"  // 市场崩盘
	MsgEconomicBoom    MessageType = "system.economic_boom" // 经济繁荣
	MsgInflationChange MessageType = "system.inflation"     // 通胀变化
	MsgInterestChange  MessageType = "system.interest_rate" // 利率变化
	MsgNewsEvent       MessageType = "system.news"          // 新闻事件
	MsgSeasonalEvent   MessageType = "system.seasonal"      // 季节性事件
)

// ===== 通用消息类型 =====
const (
	// 错误和通知
	MsgError   MessageType = "error"   // 错误消息
	MsgSuccess MessageType = "success" // 成功消息
	MsgInfo    MessageType = "info"    // 信息通知
	MsgWarning MessageType = "warning" // 警告消息
)

// ===== 连接管理相关数据结构 =====

// ConnectData 用户连接数据结构
type ConnectData struct {
	UserID      string `json:"user_id"`
	UserName    string `json:"user_name"`
	CreatedAt   int64  `json:"created_at"`
	LastLoginAt int64  `json:"last_login_at"`
}

// ===== 房间管理相关数据结构 =====

// JoinRoomData 加入房间的数据结构
type JoinRoomData struct {
	UserID   string `json:"user_id"`
	UserName string `json:"user_name"`
	RoomID   string `json:"room_id"`
	Password string `json:"password,omitempty"` // 房间密码（可选）
}

// CreateRoomData 创建房间的数据结构
type CreateRoomData struct {
	RoomName   string `json:"room_name"`
	MaxPlayers int    `json:"max_players"`
	IsPrivate  bool   `json:"is_private"`
	Password   string `json:"password,omitempty"`
	GameMode   string `json:"game_mode"`  // 游戏模式
	Difficulty string `json:"difficulty"` // 难度等级
}

// RoomStateData 房间状态数据结构
type RoomStateData struct {
	RoomID     string             `json:"room_id"`
	RoomName   string             `json:"room_name"`
	Players    map[string]*Player `json:"players"`
	MaxPlayers int                `json:"max_players"`
	IsStarted  bool               `json:"is_started"`
	CreatedAt  string             `json:"created_at"`
}

// ===== 玩家操作相关数据结构 =====

// RollDiceData 投掷骰子的数据结构
type RollDiceData struct {
	DiceCount int `json:"dice_count"` // 骰子数量
}

// MakeChoiceData 做出选择的数据结构
type MakeChoiceData struct {
	EventID  string                 `json:"event_id"`
	ChoiceID string                 `json:"choice_id"`
	Params   map[string]interface{} `json:"params,omitempty"` // 选择参数
}

// BuyAssetData 购买资产的数据结构
type BuyAssetData struct {
	AssetType     string `json:"asset_type"`
	AssetName     string `json:"asset_name"`
	PurchasePrice int    `json:"purchase_price"`
	MonthlyIncome int    `json:"monthly_income"`
	Quantity      int    `json:"quantity"`
}

// SellAssetData 出售资产的数据结构
type SellAssetData struct {
	AssetID   string `json:"asset_id"`
	Quantity  int    `json:"quantity"`
	SellPrice int    `json:"sell_price,omitempty"` // 期望售价（可选）
}

// LoanData 借贷相关数据结构
type LoanData struct {
	Amount       int     `json:"amount"`
	InterestRate float64 `json:"interest_rate"`
	TermMonths   int     `json:"term_months"`
	LoanType     string  `json:"loan_type"` // 贷款类型
}

// ChatData 聊天消息数据结构
type ChatData struct {
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
	IsPublic  bool   `json:"is_public"` // 是否公开聊天
}

// ===== 服务器响应相关数据结构 =====

// DiceResultData 骰子结果数据结构
type DiceResultData struct {
	DiceValues []int  `json:"dice_values"`
	TotalSteps int    `json:"total_steps"`
	PlayerID   string `json:"player_id"`
}

// MoveResultData 移动结果数据结构
type MoveResultData struct {
	PlayerID   string `json:"player_id"`
	FromSquare int    `json:"from_square"`
	ToSquare   int    `json:"to_square"`
	SquareType string `json:"square_type"`
	SquareName string `json:"square_name"`
}

// EventTriggeredData 触发事件数据结构
type EventTriggeredData struct {
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	PlayerID    string                 `json:"player_id,omitempty"`
	Choices     []EventChoice          `json:"choices,omitempty"`
	Params      map[string]interface{} `json:"params,omitempty"`
}

// EventChoice 事件选择项
type EventChoice struct {
	ChoiceID    string `json:"choice_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Cost        int    `json:"cost,omitempty"`
	Risk        string `json:"risk,omitempty"` // 风险等级
}

// ActionResultData 操作结果数据结构
type ActionResultData struct {
	ActionType string                 `json:"action_type"`
	Success    bool                   `json:"success"`
	Message    string                 `json:"message"`
	PlayerID   string                 `json:"player_id"`
	Changes    map[string]interface{} `json:"changes,omitempty"` // 状态变化
}

// TurnChangedData 回合切换数据结构
type TurnChangedData struct {
	CurrentPlayerID  string `json:"current_player_id"`
	PreviousPlayerID string `json:"previous_player_id,omitempty"`
	TurnNumber       int    `json:"turn_number"`
	TimeLimit        int    `json:"time_limit,omitempty"` // 回合时间限制（秒）
}

// PhaseChangedData 阶段切换数据结构
type PhaseChangedData struct {
	CurrentPhase  string                 `json:"current_phase"`
	PreviousPhase string                 `json:"previous_phase,omitempty"`
	PhaseData     map[string]interface{} `json:"phase_data,omitempty"`
}

// ===== 系统事件相关数据结构 =====

// SystemEventData 系统事件数据结构
type SystemEventData struct {
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Severity    string                 `json:"severity"`           // 严重程度: low, medium, high, critical
	Duration    int                    `json:"duration,omitempty"` // 持续时间（月）
	Effects     map[string]interface{} `json:"effects,omitempty"`  // 影响效果
}

// BlackSwanEventData 黑天鹅事件数据结构
type BlackSwanEventData struct {
	EventID       string                 `json:"event_id"`
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	Probability   float64                `json:"probability"`    // 发生概率
	ImpactLevel   string                 `json:"impact_level"`   // 影响程度
	AffectedAreas []string               `json:"affected_areas"` // 受影响领域
	Effects       map[string]interface{} `json:"effects"`
}

// EconomicEventData 经济事件数据结构
type EconomicEventData struct {
	EventType            string  `json:"event_type"`             // boom, crash, inflation, deflation
	Intensity            string  `json:"intensity"`              // mild, moderate, severe
	Duration             int     `json:"duration"`               // 持续月数
	InflationRate        float64 `json:"inflation_rate"`         // 通胀率变化
	InterestRate         float64 `json:"interest_rate"`          // 利率变化
	StockMultiplier      float64 `json:"stock_multiplier"`       // 股票价格倍数
	RealEstateMultiplier float64 `json:"real_estate_multiplier"` // 房地产价格倍数
}

// ===== 通用数据结构 =====

// ErrorData 错误消息的数据结构
type ErrorData struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// SuccessData 成功消息的数据结构
type SuccessData struct {
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data,omitempty"`
}

// InfoData 信息通知的数据结构
type InfoData struct {
	Message  string `json:"message"`
	Category string `json:"category,omitempty"` // 信息分类
	Priority string `json:"priority,omitempty"` // 优先级
}

// WarningData 警告消息的数据结构
type WarningData struct {
	Message     string `json:"message"`
	WarningType string `json:"warning_type"`
	Severity    string `json:"severity"` // low, medium, high
}
