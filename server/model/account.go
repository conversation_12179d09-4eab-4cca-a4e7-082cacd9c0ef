package model

import "time"

// AccountStatus 账号状态枚举
type AccountStatus string

const (
	StatusOffline AccountStatus = "offline" // 离线
	StatusOnline  AccountStatus = "online"  // 在线
	StatusInRoom  AccountStatus = "in_room" // 在房间中
	StatusInGame  AccountStatus = "in_game" // 游戏中
)

// Account 表示玩家账号
//
// 调用场景:
//   用于管理玩家的基本账号信息、在线状态、房间状态等跨游戏会话的持久化数据
//
// 主要字段:
//   - ID: 账号唯一标识
//   - Username: 用户名
//   - Nickname: 显示昵称
//   - Status: 当前状态（离线/在线/在房间/游戏中）
//   - CurrentRoomID: 当前所在房间ID
//   - Statistics: 历史游戏统计数据
type Account struct {
	ID            string         `json:"id"`
	Username      string         `json:"username"`      // 登录用户名
	Nickname      string         `json:"nickname"`      // 显示昵称
	Avatar        string         `json:"avatar"`        // 头像URL
	Status        AccountStatus  `json:"status"`        // 当前状态
	CurrentRoomID string         `json:"current_room_id,omitempty"` // 当前房间ID
	Statistics    GameStatistics `json:"statistics"`    // 游戏统计数据
	LastLoginAt   time.Time      `json:"last_login_at"` // 最后登录时间
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}

// GameStatistics 游戏统计数据
//
// 调用场景:
//   记录玩家的历史游戏表现和统计信息
//
// 主要字段:
//   - TotalGames: 总游戏局数
//   - WinGames: 胜利局数（达成财务自由）
//   - BestCashFlow: 最佳现金流记录
//   - AverageGameTime: 平均游戏时长
type GameStatistics struct {
	TotalGames      int           `json:"total_games"`      // 总游戏局数
	WinGames        int           `json:"win_games"`        // 胜利局数
	BestCashFlow    int           `json:"best_cash_flow"`   // 最佳现金流记录
	AverageGameTime time.Duration `json:"average_game_time"` // 平均游戏时长（秒）
	LastPlayedAt    time.Time     `json:"last_played_at"`   // 最后游戏时间
}

// UpdateStatus 更新账号状态
//
// 调用场景:
//   当玩家登录、进入房间、开始游戏、离线等状态变化时调用
//
// 主要逻辑:
//   1. 更新状态字段
//   2. 根据状态更新相关字段（如房间ID）
//   3. 更新时间戳
func (a *Account) UpdateStatus(status AccountStatus, roomID ...string) {
	a.Status = status
	a.UpdatedAt = time.Now()

	switch status {
	case StatusOffline:
		a.CurrentRoomID = "" // 离线时清空房间ID
	case StatusInRoom, StatusInGame:
		if len(roomID) > 0 {
			a.CurrentRoomID = roomID[0]
		}
	}

	// 更新最后登录时间（当状态变为在线时）
	if status == StatusOnline {
		a.LastLoginAt = time.Now()
	}
}

// UpdateGameStatistics 更新游戏统计数据
//
// 调用场景:
//   游戏结束时调用，更新玩家的历史统计信息
//
// 主要逻辑:
//   1. 增加总游戏局数
//   2. 根据游戏结果更新胜利局数
//   3. 更新最佳记录
//   4. 计算平均游戏时长
func (a *Account) UpdateGameStatistics(isWin bool, finalCashFlow int, gameDuration time.Duration) {
	a.Statistics.TotalGames++
	
	if isWin {
		a.Statistics.WinGames++
	}

	// 更新最佳现金流记录
	if finalCashFlow > a.Statistics.BestCashFlow {
		a.Statistics.BestCashFlow = finalCashFlow
	}

	// 计算平均游戏时长
	if a.Statistics.TotalGames == 1 {
		a.Statistics.AverageGameTime = gameDuration
	} else {
		// 使用加权平均计算新的平均时长
		totalTime := a.Statistics.AverageGameTime * time.Duration(a.Statistics.TotalGames-1)
		a.Statistics.AverageGameTime = (totalTime + gameDuration) / time.Duration(a.Statistics.TotalGames)
	}

	a.Statistics.LastPlayedAt = time.Now()
	a.UpdatedAt = time.Now()
}

// GetWinRate 获取胜率
//
// 调用场景:
//   显示玩家统计信息时调用
//
// 主要逻辑:
//   计算胜利局数占总局数的百分比
func (a *Account) GetWinRate() float64 {
	if a.Statistics.TotalGames == 0 {
		return 0.0
	}
	return float64(a.Statistics.WinGames) / float64(a.Statistics.TotalGames) * 100
}

// IsOnline 检查账号是否在线
//
// 调用场景:
//   需要判断玩家在线状态时调用
func (a *Account) IsOnline() bool {
	return a.Status != StatusOffline
}

// IsInGame 检查账号是否在游戏中
//
// 调用场景:
//   需要判断玩家是否正在游戏时调用
func (a *Account) IsInGame() bool {
	return a.Status == StatusInGame
}
