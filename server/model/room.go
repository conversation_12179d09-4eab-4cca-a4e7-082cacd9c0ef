package model

import (
	"sync"
	"time"
)

// RoomStatus 房间状态枚举
type RoomStatus string

const (
	RoomStatusWaiting  RoomStatus = "waiting"   // 等待玩家加入
	RoomStatusInGame   RoomStatus = "in_game"   // 游戏进行中
	RoomStatusFinished RoomStatus = "finished"  // 游戏已结束
)

// RoomState 房间状态管理
//
// 调用场景:
//   管理房间的基本信息、玩家列表、房间状态等
//
// 主要职责:
//   - 房间基本信息管理（ID、名称、房主）
//   - 账号列表管理（加入、离开）
//   - 房间状态控制（等待、游戏中、结束）
//   - 房间生命周期管理
type RoomState struct {
	ID          string                `json:"id"`           // 房间唯一标识
	RoomName    string                `json:"room_name"`    // 房间名称
	RoomOwner   string                `json:"room_owner"`   // 房主账号ID
	Accounts    map[string]*Account   `json:"accounts"`     // 房间内的账号列表
	MaxPlayers  int                   `json:"max_players"`  // 最大玩家数量
	Status      RoomStatus            `json:"status"`       // 房间状态
	GameState   *GameState            `json:"game_state,omitempty"` // 游戏状态（游戏开始后才有）
	CreatedAt   time.Time             `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time             `json:"updated_at"`   // 更新时间
	mutex       sync.RWMutex          // 并发安全锁
}

// NewRoomState 创建新的房间状态
//
// 调用场景:
//   当创建新房间时调用
//
// 主要逻辑:
//   初始化房间状态的各个字段
func NewRoomState(id, roomName, roomOwner string, maxPlayers int) *RoomState {
	return &RoomState{
		ID:         id,
		RoomName:   roomName,
		RoomOwner:  roomOwner,
		Accounts:   make(map[string]*Account),
		MaxPlayers: maxPlayers,
		Status:     RoomStatusWaiting,
		GameState:  nil, // 游戏开始前为空
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
}

// AddAccount 添加账号到房间
//
// 调用场景:
//   当新玩家加入房间时调用
//
// 主要逻辑:
//   1. 检查房间是否已满
//   2. 检查游戏是否已开始
//   3. 添加账号到房间
func (rs *RoomState) AddAccount(account *Account) bool {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	if len(rs.Accounts) >= rs.MaxPlayers {
		return false // 房间已满
	}

	if rs.Status == RoomStatusInGame {
		return false // 游戏已开始，不能加入
	}

	// 更新账号状态为在房间中
	account.Status = StatusInRoom
	account.CurrentRoomID = rs.ID
	
	rs.Accounts[account.ID] = account
	rs.UpdatedAt = time.Now()
	return true
}

// RemoveAccount 从房间移除账号
//
// 调用场景:
//   当玩家离开房间时调用
func (rs *RoomState) RemoveAccount(accountID string) bool {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	account, exists := rs.Accounts[accountID]
	if !exists {
		return false
	}

	// 更新账号状态为在线
	account.Status = StatusOnline
	account.CurrentRoomID = ""
	
	delete(rs.Accounts, accountID)
	rs.UpdatedAt = time.Now()
	
	// 如果离开的是房主，转移房主权限
	if rs.RoomOwner == accountID && len(rs.Accounts) > 0 {
		for newOwnerID := range rs.Accounts {
			rs.RoomOwner = newOwnerID
			break
		}
	}
	
	return true
}

// StartGame 开始游戏
//
// 调用场景:
//   当房主点击开始游戏时调用
//
// 主要逻辑:
//   1. 检查是否有足够的玩家
//   2. 创建游戏状态
//   3. 将账号转换为游戏玩家
//   4. 设置房间状态为游戏中
func (rs *RoomState) StartGame() bool {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	if len(rs.Accounts) < 1 {
		return false // 玩家数量不足
	}

	if rs.Status != RoomStatusWaiting {
		return false // 房间状态不正确
	}

	// 创建游戏状态
	rs.GameState = NewGameState(rs.ID)
	
	// 将房间内的账号转换为游戏玩家
	for _, account := range rs.Accounts {
		player := &Player{
			GameID:            rs.ID,
			AccountID:         account.ID,
			Profession:        "Engineer", // 默认职业
			Cash:              5000,       // 初始现金
			CashFlow:          1000,       // 初始现金流
			Assets:            []Asset{},
			GameTurn:          1,
			IsFinanciallyFree: false,
			Incomes: []Income{
				{
					ID:        "salary",
					Type:      "salary",
					Source:    "Job",
					Amount:    3000,
					IsPassive: false,
				},
			},
			Expenses: []Expense{
				{
					ID:          "living",
					Type:        "living",
					Description: "Living expenses",
					Amount:      2000,
					IsFixed:     true,
				},
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		// 重新计算现金流
		player.CalculateCashFlow()
		
		// 添加到游戏状态
		rs.GameState.AddPlayer(player)
		
		// 更新账号状态为游戏中
		account.Status = StatusInGame
	}

	// 设置房间状态为游戏中
	rs.Status = RoomStatusInGame
	rs.UpdatedAt = time.Now()
	
	// 开始游戏
	return rs.GameState.StartGame()
}

// EndGame 结束游戏
//
// 调用场景:
//   当游戏结束时调用
func (rs *RoomState) EndGame() {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	// 更新所有账号状态为在房间中
	for _, account := range rs.Accounts {
		account.Status = StatusInRoom
	}

	rs.Status = RoomStatusFinished
	rs.UpdatedAt = time.Now()
}

// GetAccountCount 获取当前账号数量
func (rs *RoomState) GetAccountCount() int {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()
	return len(rs.Accounts)
}

// GetAccount 获取指定账号
func (rs *RoomState) GetAccount(accountID string) (*Account, bool) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()
	account, exists := rs.Accounts[accountID]
	return account, exists
}

// IsOwner 检查是否为房主
func (rs *RoomState) IsOwner(accountID string) bool {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()
	return rs.RoomOwner == accountID
}

// CanStartGame 检查是否可以开始游戏
func (rs *RoomState) CanStartGame() bool {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()
	return rs.Status == RoomStatusWaiting && len(rs.Accounts) >= 1
}
